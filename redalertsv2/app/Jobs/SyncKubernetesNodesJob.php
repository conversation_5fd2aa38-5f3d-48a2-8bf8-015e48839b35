<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use App\Services\KubernetesService;
use App\Models\KubernetesNode;
use App\Models\KubernetesNodePod;
use App\Models\KubernetesNodeEvent;
use Carbon\Carbon;

class SyncKubernetesNodesJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 300; // 5 minutes timeout
    public $tries = 3;

    protected $clusterName;
    protected $kubeconfigPath;

    /**
     * Create a new job instance.
     */
    public function __construct(string $clusterName, string $kubeconfigPath)
    {
        $this->clusterName = $clusterName;
        $this->kubeconfigPath = $kubeconfigPath;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info("Starting Kubernetes sync for cluster: {$this->clusterName}");

            $service = new KubernetesService($this->kubeconfigPath);

            // Sync nodes
            $this->syncNodes($service);

            // Sync pods
            $this->syncPods($service);

            // Sync events
            $this->syncEvents($service);

            // Update node metrics if available
            $this->syncNodeMetrics($service);

            // Mark inactive nodes/pods/events
            $this->markInactiveResources();

            Log::info("Completed Kubernetes sync for cluster: {$this->clusterName}");

        } catch (\Exception $e) {
            Log::error("Failed to sync Kubernetes data for cluster {$this->clusterName}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Sync nodes from Kubernetes API
     */
    protected function syncNodes(KubernetesService $service): void
    {
        try {
            $nodesResponse = $service->getNodes();
            $nodes = $nodesResponse['items'] ?? [];

            $syncedNodeNames = [];

            foreach ($nodes as $nodeData) {
                $node = KubernetesNode::updateFromApiData($this->clusterName, $nodeData);
                $syncedNodeNames[] = $node->name;
            }

            // Mark nodes not in current sync as inactive
            KubernetesNode::forCluster($this->clusterName)
                ->whereNotIn('name', $syncedNodeNames)
                ->update(['is_active' => false]);

            Log::info("Synced " . count($nodes) . " nodes for cluster: {$this->clusterName}");

        } catch (\Exception $e) {
            Log::error("Failed to sync nodes for cluster {$this->clusterName}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Sync pods from Kubernetes API
     */
    protected function syncPods(KubernetesService $service): void
    {
        try {
            $podsResponse = $service->getPods();
            $pods = $podsResponse['items'] ?? [];

            $syncedPodUids = [];

            foreach ($pods as $podData) {
                // Only sync pods that are assigned to a node
                if (isset($podData['spec']['nodeName'])) {
                    $pod = KubernetesNodePod::updateFromApiData($this->clusterName, $podData);
                    if ($pod) {
                        $syncedPodUids[] = $pod->uid;
                    }
                }
            }

            // Mark pods not in current sync as inactive
            KubernetesNodePod::forCluster($this->clusterName)
                ->whereNotIn('uid', $syncedPodUids)
                ->update(['is_active' => false]);

            Log::info("Synced " . count($syncedPodUids) . " pods for cluster: {$this->clusterName}");

        } catch (\Exception $e) {
            Log::error("Failed to sync pods for cluster {$this->clusterName}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Sync events from Kubernetes API
     */
    protected function syncEvents(KubernetesService $service): void
    {
        try {
            $eventsResponse = $service->getEvents();
            $events = $eventsResponse['items'] ?? [];

            $syncedEventUids = [];

            foreach ($events as $eventData) {
                $event = KubernetesNodeEvent::updateFromApiData($this->clusterName, $eventData);
                if ($event) {
                    $syncedEventUids[] = $event->uid;
                }
            }

            // Mark events not in current sync as inactive (only recent ones)
            KubernetesNodeEvent::forCluster($this->clusterName)
                ->where('last_timestamp', '>=', now()->subHours(24))
                ->whereNotIn('uid', $syncedEventUids)
                ->update(['is_active' => false]);

            Log::info("Synced " . count($syncedEventUids) . " node events for cluster: {$this->clusterName}");

        } catch (\Exception $e) {
            Log::error("Failed to sync events for cluster {$this->clusterName}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Sync node metrics if metrics-server is available
     */
    protected function syncNodeMetrics(KubernetesService $service): void
    {
        try {
            $metricsResponse = $service->getNodeMetrics();
            $metrics = $metricsResponse['items'] ?? [];

            foreach ($metrics as $metricData) {
                $nodeName = $metricData['metadata']['name'] ?? null;
                if (!$nodeName) continue;

                $node = KubernetesNode::forCluster($this->clusterName)
                    ->where('name', $nodeName)
                    ->first();

                if ($node) {
                    $usage = $metricData['usage'] ?? [];
                    
                    $node->update([
                        'cpu_usage_cores' => $this->parseCpuToNanocores($usage['cpu'] ?? '0'),
                        'memory_usage_bytes' => $this->parseMemoryToBytes($usage['memory'] ?? '0'),
                        'metrics_updated_at' => now(),
                    ]);
                }
            }

            Log::info("Synced metrics for " . count($metrics) . " nodes in cluster: {$this->clusterName}");

        } catch (\Exception $e) {
            Log::warning("Failed to sync node metrics for cluster {$this->clusterName}: " . $e->getMessage());
            // Don't throw here as metrics are optional
        }
    }

    /**
     * Mark resources as inactive if they haven't been synced recently
     */
    protected function markInactiveResources(): void
    {
        $cutoffTime = now()->subMinutes(10);

        // Mark old nodes as inactive
        KubernetesNode::forCluster($this->clusterName)
            ->where('last_synced_at', '<', $cutoffTime)
            ->update(['is_active' => false]);

        // Mark old pods as inactive
        KubernetesNodePod::forCluster($this->clusterName)
            ->where('last_synced_at', '<', $cutoffTime)
            ->update(['is_active' => false]);

        // Clean up old events (older than 7 days)
        KubernetesNodeEvent::forCluster($this->clusterName)
            ->where('last_timestamp', '<', now()->subDays(7))
            ->delete();
    }

    /**
     * Parse CPU to nanocores
     */
    protected function parseCpuToNanocores(string $cpu): int
    {
        if (str_ends_with($cpu, 'n')) {
            return (int) str_replace('n', '', $cpu);
        }

        if (str_ends_with($cpu, 'u')) {
            return (int) str_replace('u', '', $cpu) * 1000;
        }

        if (str_ends_with($cpu, 'm')) {
            return (int) str_replace('m', '', $cpu) * 1000000;
        }

        // Assume cores
        return (int) ((float) $cpu * 1000000000);
    }

    /**
     * Parse memory to bytes
     */
    protected function parseMemoryToBytes(string $memory): int
    {
        if (is_numeric($memory)) {
            return (int) $memory;
        }

        $units = [
            'Ki' => 1024,
            'Mi' => 1024 * 1024,
            'Gi' => 1024 * 1024 * 1024,
            'Ti' => 1024 * 1024 * 1024 * 1024,
            'K' => 1000,
            'M' => 1000 * 1000,
            'G' => 1000 * 1000 * 1000,
            'T' => 1000 * 1000 * 1000 * 1000,
        ];

        foreach ($units as $unit => $multiplier) {
            if (str_ends_with($memory, $unit)) {
                return (int) (floatval(str_replace($unit, '', $memory)) * $multiplier);
            }
        }

        return (int) $memory;
    }

    /**
     * Handle job failure
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("SyncKubernetesNodesJob failed for cluster {$this->clusterName}: " . $exception->getMessage());
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class KubernetesNodePod extends Model
{
    use HasFactory;

    protected $table = 'kubernetes_node_pods';

    protected $fillable = [
        'cluster_name',
        'node_name',
        'pod_name',
        'namespace',
        'uid',
        'status',
        'phase',
        'ready',
        'restart_count',
        'created_at_k8s',
        'labels',
        'annotations',
        'resource_requests',
        'resource_limits',
        'containers',
        'container_statuses',
        'last_synced_at',
        'is_active',
    ];

    protected $casts = [
        'ready' => 'boolean',
        'restart_count' => 'integer',
        'created_at_k8s' => 'datetime',
        'labels' => 'array',
        'annotations' => 'array',
        'resource_requests' => 'array',
        'resource_limits' => 'array',
        'containers' => 'array',
        'container_statuses' => 'array',
        'last_synced_at' => 'datetime',
        'is_active' => 'boolean',
    ];

    /**
     * Get the node this pod belongs to
     */
    public function node(): BelongsTo
    {
        return $this->belongsTo(KubernetesNode::class, 'node_name', 'name')
                    ->where('cluster_name', $this->cluster_name);
    }

    /**
     * Scope for active pods
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for specific cluster
     */
    public function scopeForCluster($query, $clusterName)
    {
        return $query->where('cluster_name', $clusterName);
    }

    /**
     * Scope for specific node
     */
    public function scopeForNode($query, $nodeName)
    {
        return $query->where('node_name', $nodeName);
    }

    /**
     * Scope for running pods
     */
    public function scopeRunning($query)
    {
        return $query->where('status', 'Running');
    }

    /**
     * Get pod age in human readable format
     */
    public function getAgeAttribute()
    {
        if (!$this->created_at_k8s) {
            return 'Unknown';
        }

        return $this->created_at_k8s->diffForHumans();
    }

    /**
     * Get ready status as string
     */
    public function getReadyStatusAttribute()
    {
        return $this->ready ? '1/1' : '0/1';
    }

    /**
     * Check if pod is healthy (running and ready)
     */
    public function getIsHealthyAttribute()
    {
        return $this->status === 'Running' && $this->ready;
    }

    /**
     * Update pod from Kubernetes API data
     */
    public static function updateFromApiData($clusterName, $podData)
    {
        $pod = static::updateOrCreate(
            [
                'cluster_name' => $clusterName,
                'pod_name' => $podData['metadata']['name'],
                'namespace' => $podData['metadata']['namespace'],
            ],
            [
                'node_name' => $podData['spec']['nodeName'] ?? null,
                'uid' => $podData['metadata']['uid'],
                'status' => $podData['status']['phase'] ?? 'Unknown',
                'phase' => $podData['status']['phase'] ?? null,
                'ready' => static::isPodReady($podData),
                'restart_count' => static::getTotalRestartCount($podData),
                'created_at_k8s' => isset($podData['metadata']['creationTimestamp']) 
                    ? Carbon::parse($podData['metadata']['creationTimestamp']) 
                    : null,
                'labels' => $podData['metadata']['labels'] ?? [],
                'annotations' => $podData['metadata']['annotations'] ?? [],
                'resource_requests' => static::extractResourceRequests($podData),
                'resource_limits' => static::extractResourceLimits($podData),
                'containers' => static::extractContainers($podData),
                'container_statuses' => $podData['status']['containerStatuses'] ?? [],
                'last_synced_at' => now(),
                'is_active' => true,
            ]
        );

        return $pod;
    }

    /**
     * Check if pod is ready
     */
    private static function isPodReady($podData)
    {
        $conditions = $podData['status']['conditions'] ?? [];
        
        foreach ($conditions as $condition) {
            if ($condition['type'] === 'Ready') {
                return $condition['status'] === 'True';
            }
        }

        return false;
    }

    /**
     * Get total restart count from all containers
     */
    private static function getTotalRestartCount($podData)
    {
        $totalRestarts = 0;
        $containerStatuses = $podData['status']['containerStatuses'] ?? [];
        
        foreach ($containerStatuses as $status) {
            $totalRestarts += $status['restartCount'] ?? 0;
        }

        return $totalRestarts;
    }

    /**
     * Extract resource requests from containers
     */
    private static function extractResourceRequests($podData)
    {
        $requests = ['cpu' => 0, 'memory' => 0];
        $containers = $podData['spec']['containers'] ?? [];
        
        foreach ($containers as $container) {
            $resources = $container['resources']['requests'] ?? [];
            
            if (isset($resources['cpu'])) {
                $requests['cpu'] += static::parseCpuToMillicores($resources['cpu']);
            }
            
            if (isset($resources['memory'])) {
                $requests['memory'] += static::parseMemoryToBytes($resources['memory']);
            }
        }

        return $requests;
    }

    /**
     * Extract resource limits from containers
     */
    private static function extractResourceLimits($podData)
    {
        $limits = ['cpu' => 0, 'memory' => 0];
        $containers = $podData['spec']['containers'] ?? [];
        
        foreach ($containers as $container) {
            $resources = $container['resources']['limits'] ?? [];
            
            if (isset($resources['cpu'])) {
                $limits['cpu'] += static::parseCpuToMillicores($resources['cpu']);
            }
            
            if (isset($resources['memory'])) {
                $limits['memory'] += static::parseMemoryToBytes($resources['memory']);
            }
        }

        return $limits;
    }

    /**
     * Extract container information
     */
    private static function extractContainers($podData)
    {
        $containers = [];
        $containerSpecs = $podData['spec']['containers'] ?? [];
        
        foreach ($containerSpecs as $container) {
            $containers[] = [
                'name' => $container['name'],
                'image' => $container['image'],
                'ports' => $container['ports'] ?? [],
                'env' => $container['env'] ?? [],
            ];
        }

        return $containers;
    }

    /**
     * Parse CPU to millicores
     */
    private static function parseCpuToMillicores($cpu)
    {
        if (str_ends_with($cpu, 'm')) {
            return (int) str_replace('m', '', $cpu);
        }

        return (float) $cpu * 1000;
    }

    /**
     * Parse memory to bytes
     */
    private static function parseMemoryToBytes($memory)
    {
        if (is_numeric($memory)) {
            return (int) $memory;
        }

        $units = [
            'Ki' => 1024,
            'Mi' => 1024 * 1024,
            'Gi' => 1024 * 1024 * 1024,
            'Ti' => 1024 * 1024 * 1024 * 1024,
            'K' => 1000,
            'M' => 1000 * 1000,
            'G' => 1000 * 1000 * 1000,
            'T' => 1000 * 1000 * 1000 * 1000,
        ];

        foreach ($units as $unit => $multiplier) {
            if (str_ends_with($memory, $unit)) {
                return (int) (floatval(str_replace($unit, '', $memory)) * $multiplier);
            }
        }

        return (int) $memory;
    }
}

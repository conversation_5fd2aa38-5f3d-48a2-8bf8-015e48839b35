<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class KubernetesNodeEvent extends Model
{
    use HasFactory;

    protected $table = 'kubernetes_node_events';

    protected $fillable = [
        'cluster_name',
        'node_name',
        'uid',
        'type',
        'reason',
        'message',
        'source_component',
        'source_host',
        'first_timestamp',
        'last_timestamp',
        'count',
        'involved_object_kind',
        'involved_object_name',
        'involved_object_namespace',
        'involved_object_uid',
        'last_synced_at',
        'is_active',
    ];

    protected $casts = [
        'first_timestamp' => 'datetime',
        'last_timestamp' => 'datetime',
        'count' => 'integer',
        'last_synced_at' => 'datetime',
        'is_active' => 'boolean',
    ];

    /**
     * Get the node this event belongs to
     */
    public function node(): BelongsTo
    {
        return $this->belongsTo(KubernetesNode::class, 'node_name', 'name')
                    ->where('cluster_name', $this->cluster_name);
    }

    /**
     * Scope for active events
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for specific cluster
     */
    public function scopeForCluster($query, $clusterName)
    {
        return $query->where('cluster_name', $clusterName);
    }

    /**
     * Scope for specific node
     */
    public function scopeForNode($query, $nodeName)
    {
        return $query->where('node_name', $nodeName);
    }

    /**
     * Scope for warning events
     */
    public function scopeWarnings($query)
    {
        return $query->where('type', 'Warning');
    }

    /**
     * Scope for normal events
     */
    public function scopeNormal($query)
    {
        return $query->where('type', 'Normal');
    }

    /**
     * Scope for recent events
     */
    public function scopeRecent($query, $hours = 24)
    {
        return $query->where('last_timestamp', '>=', now()->subHours($hours));
    }

    /**
     * Get event age in human readable format
     */
    public function getAgeAttribute()
    {
        if (!$this->last_timestamp) {
            return 'Unknown';
        }

        return $this->last_timestamp->diffForHumans();
    }

    /**
     * Check if event is a warning
     */
    public function getIsWarningAttribute()
    {
        return $this->type === 'Warning';
    }

    /**
     * Get formatted message (truncated if too long)
     */
    public function getFormattedMessageAttribute()
    {
        if (!$this->message) {
            return '';
        }

        return strlen($this->message) > 100 
            ? substr($this->message, 0, 100) . '...' 
            : $this->message;
    }

    /**
     * Update event from Kubernetes API data
     */
    public static function updateFromApiData($clusterName, $eventData)
    {
        // Only process events related to nodes
        if (($eventData['involvedObject']['kind'] ?? '') !== 'Node') {
            return null;
        }

        $event = static::updateOrCreate(
            [
                'cluster_name' => $clusterName,
                'uid' => $eventData['metadata']['uid'],
            ],
            [
                'node_name' => $eventData['involvedObject']['name'] ?? null,
                'type' => $eventData['type'] ?? 'Normal',
                'reason' => $eventData['reason'] ?? null,
                'message' => $eventData['message'] ?? null,
                'source_component' => $eventData['source']['component'] ?? null,
                'source_host' => $eventData['source']['host'] ?? null,
                'first_timestamp' => isset($eventData['firstTimestamp']) 
                    ? Carbon::parse($eventData['firstTimestamp']) 
                    : null,
                'last_timestamp' => isset($eventData['lastTimestamp']) 
                    ? Carbon::parse($eventData['lastTimestamp']) 
                    : (isset($eventData['eventTime']) ? Carbon::parse($eventData['eventTime']) : null),
                'count' => $eventData['count'] ?? 1,
                'involved_object_kind' => $eventData['involvedObject']['kind'] ?? 'Node',
                'involved_object_name' => $eventData['involvedObject']['name'] ?? null,
                'involved_object_namespace' => $eventData['involvedObject']['namespace'] ?? null,
                'involved_object_uid' => $eventData['involvedObject']['uid'] ?? null,
                'last_synced_at' => now(),
                'is_active' => true,
            ]
        );

        return $event;
    }

    /**
     * Clean up old events (older than specified days)
     */
    public static function cleanupOldEvents($days = 7)
    {
        return static::where('last_timestamp', '<', now()->subDays($days))
                    ->delete();
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class KubernetesNode extends Model
{
    use HasFactory;

    protected $table = 'kubernetes_nodes';

    protected $fillable = [
        'cluster_name',
        'name',
        'uid',
        'created_at_k8s',
        'resource_version',
        'labels',
        'annotations',
        'status',
        'conditions',
        'addresses',
        'os_image',
        'kernel_version',
        'container_runtime',
        'kubelet_version',
        'architecture',
        'operating_system',
        'capacity',
        'allocatable',
        'roles',
        'taints',
        'cpu_usage_cores',
        'memory_usage_bytes',
        'metrics_updated_at',
        'last_synced_at',
        'is_active',
    ];

    protected $casts = [
        'labels' => 'array',
        'annotations' => 'array',
        'conditions' => 'array',
        'addresses' => 'array',
        'capacity' => 'array',
        'allocatable' => 'array',
        'roles' => 'array',
        'taints' => 'array',
        'created_at_k8s' => 'datetime',
        'metrics_updated_at' => 'datetime',
        'last_synced_at' => 'datetime',
        'is_active' => 'boolean',
        'cpu_usage_cores' => 'integer',
        'memory_usage_bytes' => 'integer',
    ];

    /**
     * Get the pods running on this node
     */
    public function pods(): HasMany
    {
        return $this->hasMany(KubernetesNodePod::class, 'node_name', 'name')
                    ->where('cluster_name', $this->cluster_name)
                    ->where('is_active', true);
    }

    /**
     * Get the events related to this node
     */
    public function events(): HasMany
    {
        return $this->hasMany(KubernetesNodeEvent::class, 'node_name', 'name')
                    ->where('cluster_name', $this->cluster_name)
                    ->where('is_active', true)
                    ->orderBy('last_timestamp', 'desc');
    }

    /**
     * Get recent events (last 20)
     */
    public function recentEvents(): HasMany
    {
        return $this->events()->limit(20);
    }

    /**
     * Scope for active nodes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for specific cluster
     */
    public function scopeForCluster($query, $clusterName)
    {
        return $query->where('cluster_name', $clusterName);
    }

    /**
     * Scope for ready nodes
     */
    public function scopeReady($query)
    {
        return $query->where('status', 'Ready');
    }

    /**
     * Get node age in human readable format
     */
    public function getAgeAttribute()
    {
        if (!$this->created_at_k8s) {
            return 'Unknown';
        }

        return $this->created_at_k8s->diffForHumans();
    }

    /**
     * Get formatted CPU capacity
     */
    public function getFormattedCpuCapacityAttribute()
    {
        if (!$this->capacity || !isset($this->capacity['cpu'])) {
            return '0';
        }

        return $this->capacity['cpu'];
    }

    /**
     * Get formatted memory capacity
     */
    public function getFormattedMemoryCapacityAttribute()
    {
        if (!$this->capacity || !isset($this->capacity['memory'])) {
            return '0 B';
        }

        return $this->formatBytes($this->capacity['memory']);
    }

    /**
     * Get node roles as string
     */
    public function getRolesStringAttribute()
    {
        if (!$this->roles || empty($this->roles)) {
            return 'worker';
        }

        return implode(', ', $this->roles);
    }

    /**
     * Check if node is ready
     */
    public function getIsReadyAttribute()
    {
        return $this->status === 'Ready';
    }

    /**
     * Get running pods count
     */
    public function getRunningPodsCountAttribute()
    {
        return $this->pods()->where('status', 'Running')->count();
    }

    /**
     * Get total pods count
     */
    public function getTotalPodsCountAttribute()
    {
        return $this->pods()->count();
    }

    /**
     * Get CPU usage percentage (if metrics available)
     */
    public function getCpuUsagePercentageAttribute()
    {
        if (!$this->cpu_usage_cores || !$this->capacity || !isset($this->capacity['cpu'])) {
            return null;
        }

        $capacityCores = (float) $this->capacity['cpu'];
        $usageCores = $this->cpu_usage_cores / 1000000000; // Convert nanocores to cores

        return round(($usageCores / $capacityCores) * 100, 2);
    }

    /**
     * Get memory usage percentage (if metrics available)
     */
    public function getMemoryUsagePercentageAttribute()
    {
        if (!$this->memory_usage_bytes || !$this->capacity || !isset($this->capacity['memory'])) {
            return null;
        }

        $capacityBytes = $this->parseMemoryToBytes($this->capacity['memory']);
        $usageBytes = $this->memory_usage_bytes;

        return round(($usageBytes / $capacityBytes) * 100, 2);
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes)
    {
        if (is_string($bytes)) {
            $bytes = $this->parseMemoryToBytes($bytes);
        }

        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * Parse Kubernetes memory format to bytes
     */
    private function parseMemoryToBytes($memory)
    {
        if (is_numeric($memory)) {
            return (int) $memory;
        }

        $units = [
            'Ki' => 1024,
            'Mi' => 1024 * 1024,
            'Gi' => 1024 * 1024 * 1024,
            'Ti' => 1024 * 1024 * 1024 * 1024,
            'K' => 1000,
            'M' => 1000 * 1000,
            'G' => 1000 * 1000 * 1000,
            'T' => 1000 * 1000 * 1000 * 1000,
        ];

        foreach ($units as $unit => $multiplier) {
            if (str_ends_with($memory, $unit)) {
                return (int) (floatval(str_replace($unit, '', $memory)) * $multiplier);
            }
        }

        return (int) $memory;
    }

    /**
     * Update node from Kubernetes API data
     */
    public static function updateFromApiData($clusterName, $nodeData)
    {
        $node = static::updateOrCreate(
            [
                'cluster_name' => $clusterName,
                'name' => $nodeData['metadata']['name'],
            ],
            [
                'uid' => $nodeData['metadata']['uid'],
                'created_at_k8s' => isset($nodeData['metadata']['creationTimestamp']) 
                    ? Carbon::parse($nodeData['metadata']['creationTimestamp']) 
                    : null,
                'resource_version' => $nodeData['metadata']['resourceVersion'] ?? null,
                'labels' => $nodeData['metadata']['labels'] ?? [],
                'annotations' => $nodeData['metadata']['annotations'] ?? [],
                'status' => static::determineNodeStatus($nodeData),
                'conditions' => $nodeData['status']['conditions'] ?? [],
                'addresses' => $nodeData['status']['addresses'] ?? [],
                'os_image' => $nodeData['status']['nodeInfo']['osImage'] ?? null,
                'kernel_version' => $nodeData['status']['nodeInfo']['kernelVersion'] ?? null,
                'container_runtime' => $nodeData['status']['nodeInfo']['containerRuntimeVersion'] ?? null,
                'kubelet_version' => $nodeData['status']['nodeInfo']['kubeletVersion'] ?? null,
                'architecture' => $nodeData['status']['nodeInfo']['architecture'] ?? null,
                'operating_system' => $nodeData['status']['nodeInfo']['operatingSystem'] ?? null,
                'capacity' => $nodeData['status']['capacity'] ?? [],
                'allocatable' => $nodeData['status']['allocatable'] ?? [],
                'roles' => static::extractNodeRoles($nodeData),
                'taints' => $nodeData['spec']['taints'] ?? [],
                'last_synced_at' => now(),
                'is_active' => true,
            ]
        );

        return $node;
    }

    /**
     * Determine node status from conditions
     */
    private static function determineNodeStatus($nodeData)
    {
        $conditions = $nodeData['status']['conditions'] ?? [];
        
        foreach ($conditions as $condition) {
            if ($condition['type'] === 'Ready') {
                return $condition['status'] === 'True' ? 'Ready' : 'NotReady';
            }
        }

        return 'Unknown';
    }

    /**
     * Extract node roles from labels
     */
    private static function extractNodeRoles($nodeData)
    {
        $labels = $nodeData['metadata']['labels'] ?? [];
        $roles = [];

        foreach ($labels as $key => $value) {
            if (str_starts_with($key, 'node-role.kubernetes.io/')) {
                $role = str_replace('node-role.kubernetes.io/', '', $key);
                $roles[] = $role;
            }
        }

        return empty($roles) ? ['worker'] : $roles;
    }
}

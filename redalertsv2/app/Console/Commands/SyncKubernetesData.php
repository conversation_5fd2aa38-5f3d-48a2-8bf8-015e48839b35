<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use App\Jobs\SyncKubernetesNodesJob;
use Illuminate\Support\Facades\File;

class SyncKubernetesData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'kubernetes:sync 
                            {--cluster= : Specific cluster to sync (optional)}
                            {--force : Force sync even if recently synced}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync Kubernetes cluster data to database for real-time access';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting Kubernetes data synchronization...');

        $kubeconfigPath = env('KUBECONFIG_PATH', storage_path('app/kubeconfigs'));
        
        if (!File::exists($kubeconfigPath)) {
            $this->error("Kubeconfig directory not found: {$kubeconfigPath}");
            return 1;
        }

        $specificCluster = $this->option('cluster');
        $force = $this->option('force');

        if ($specificCluster) {
            $this->syncCluster($specificCluster, $kubeconfigPath, $force);
        } else {
            $this->syncAllClusters($kubeconfigPath, $force);
        }

        $this->info('Kubernetes data synchronization completed.');
        return 0;
    }

    /**
     * Sync all available clusters
     */
    protected function syncAllClusters(string $kubeconfigPath, bool $force): void
    {
        $clusters = File::files($kubeconfigPath);
        
        if (empty($clusters)) {
            $this->warn('No kubeconfig files found in: ' . $kubeconfigPath);
            return;
        }

        $this->info('Found ' . count($clusters) . ' cluster(s) to sync');

        foreach ($clusters as $file) {
            $clusterName = $file->getFilenameWithoutExtension();
            $this->syncCluster($clusterName, $file->getPathname(), $force);
        }
    }

    /**
     * Sync a specific cluster
     */
    protected function syncCluster(string $clusterName, string $configPath, bool $force): void
    {
        try {
            // Check if cluster config exists
            if (!File::exists($configPath)) {
                $fullPath = env('KUBECONFIG_PATH', storage_path('app/kubeconfigs')) . '/' . $clusterName;
                if (!File::exists($fullPath)) {
                    $this->error("Kubeconfig not found for cluster: {$clusterName}");
                    return;
                }
                $configPath = $fullPath;
            }

            // Check if we should skip this sync (unless forced)
            if (!$force && $this->shouldSkipSync($clusterName)) {
                $this->line("Skipping {$clusterName} - recently synced");
                return;
            }

            $this->line("Syncing cluster: {$clusterName}");

            // Dispatch the sync job
            SyncKubernetesNodesJob::dispatch($clusterName, $configPath);

            $this->info("✓ Dispatched sync job for cluster: {$clusterName}");

            // Update last sync time
            $this->updateLastSyncTime($clusterName);

        } catch (\Exception $e) {
            $this->error("Failed to sync cluster {$clusterName}: " . $e->getMessage());
            Log::error("Kubernetes sync command failed for cluster {$clusterName}: " . $e->getMessage());
        }
    }

    /**
     * Check if we should skip syncing this cluster
     */
    protected function shouldSkipSync(string $clusterName): bool
    {
        $cacheKey = "kubernetes_last_sync_{$clusterName}";
        $lastSync = cache($cacheKey);
        
        if (!$lastSync) {
            return false;
        }

        // Skip if synced within the last 25 seconds (allowing 5 second buffer for 30s schedule)
        return now()->diffInSeconds($lastSync) < 25;
    }

    /**
     * Update the last sync time for a cluster
     */
    protected function updateLastSyncTime(string $clusterName): void
    {
        $cacheKey = "kubernetes_last_sync_{$clusterName}";
        cache([$cacheKey => now()], now()->addMinutes(5));
    }
}

<?php

namespace App\Livewire\Kubernetes;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use App\Services\CachedKubernetesService;
use App\Traits\HasKubernetesTable;
use App\Models\KubernetesNode;
use App\Models\KubernetesNodePod;
use App\Models\KubernetesNodeEvent;
use Carbon\Carbon;

class NodeList extends Component
{
    use HasKubernetesTable;

    public $nodes = [];
    public $loading = true;
    public $error = null;
    public $selectedCluster = null;

    // Node details panel
    public $selectedNode = null;
    public $selectedNodeData = null;
    public $selectedNodePods = [];
    public $selectedNodeEvents = [];
    public $selectedNodeMetrics = null;
    public $showNodeDetails = false;
    public $nodeDetailsLoading = false;

    public function mount()
    {
        // Initialize trait properties
        $this->searchTerm = '';
        $this->selectedNamespaces = ['all'];
        $this->showNamespaceFilter = false;
        $this->sortField = '';
        $this->sortDirection = 'asc';
        $this->perPage = 10;
        $this->currentPage = 1;
        $this->totalItems = 0;

        // Check if user is authenticated
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        // Try both session keys for compatibility
        $this->selectedCluster = session('selectedCluster') ?? session('selected_cluster');
        if ($this->selectedCluster) {
            $this->loadData();
            // Preload common data in background for faster node switching
            $this->preloadCommonData();
        } else {
            $this->error = 'Please select a cluster first';
            $this->loading = false;
        }
    }

    public function preloadCommonData()
    {
        try {
            $kubeconfigPath = env('KUBECONFIG_PATH', storage_path('app/kubeconfigs')) . '/' . $this->selectedCluster;
            $service = new CachedKubernetesService($kubeconfigPath);

            // Preload pods and events in background (this will cache them)
            $service->getPods();
            $service->getEvents();
        } catch (\Exception $e) {
            // Silently fail preloading - not critical
            \Illuminate\Support\Facades\Log::info('Preload failed: ' . $e->getMessage());
        }
    }

    public function loadData()
    {
        $this->loading = true;
        $this->error = null;

        try {
            // Load nodes from database instead of API
            $dbNodes = KubernetesNode::forCluster($this->selectedCluster)
                ->active()
                ->with(['pods' => function($query) {
                    $query->active();
                }, 'recentEvents'])
                ->orderBy('name')
                ->get();

            if ($dbNodes->isEmpty()) {
                // If no nodes in database, trigger a sync and show message
                $this->triggerSync();
                $this->error = 'No nodes found in database. Syncing data in background...';
                $this->nodes = [];
                $this->totalItems = 0;
            } else {
                // Convert database models to API-like format for compatibility
                $this->nodes = $dbNodes->map(function ($node) {
                    return $this->convertNodeToApiFormat($node);
                })->toArray();
                $this->totalItems = count($this->nodes);

                // If a node is selected, refresh its details
                if ($this->selectedNode && $this->showNodeDetails) {
                    $this->loadNodeDetailsFromDatabase();
                }
            }

        } catch (\Exception $e) {
            $this->error = 'Failed to load nodes: ' . $e->getMessage();
            $this->nodes = [];
            $this->totalItems = 0;
        } finally {
            $this->loading = false;
        }
    }

    public function refreshData()
    {
        try {
            // Refresh the selected cluster from session
            $this->selectedCluster = session('selectedCluster') ?? session('selected_cluster');

            if (!$this->selectedCluster) {
                $this->error = 'Please select a cluster first';
                return;
            }

            $kubeconfigPath = env('KUBECONFIG_PATH', storage_path('app/kubeconfigs')) . '/' . $this->selectedCluster;
            $service = new CachedKubernetesService($kubeconfigPath);

            // Force refresh cache
            $service->clearCache();
            $this->clearNodeCache();
            $this->loadData();

            // If a node is selected, reload its details
            if ($this->selectedNode) {
                $this->loadNodeDetailsAsync();
            }

            $this->dispatch('notify', [
                'type' => 'success',
                'message' => 'Nodes data refreshed successfully'
            ]);
        } catch (\Exception $e) {
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => 'Failed to refresh data: ' . $e->getMessage()
            ]);
        }
    }

    public function clearNodeCache()
    {
        try {
            $pattern = 'node_details_' . md5($this->selectedCluster . '_*');
            $keys = \Illuminate\Support\Facades\Redis::keys($pattern);
            if (!empty($keys)) {
                \Illuminate\Support\Facades\Redis::del($keys);
            }
        } catch (\Exception $e) {
            // Silently fail cache clearing
        }
    }

    /**
     * Trigger a sync for the current cluster
     */
    protected function triggerSync()
    {
        try {
            $kubeconfigPath = env('KUBECONFIG_PATH', storage_path('app/kubeconfigs')) . '/' . $this->selectedCluster;
            if (file_exists($kubeconfigPath)) {
                \App\Jobs\SyncKubernetesNodesJob::dispatch($this->selectedCluster, $kubeconfigPath);
            }
        } catch (\Exception $e) {
            // Silently fail sync trigger
        }
    }

    /**
     * Convert database node model to API-like format for compatibility
     */
    protected function convertNodeToApiFormat(KubernetesNode $node)
    {
        return [
            'metadata' => [
                'name' => $node->name,
                'uid' => $node->uid,
                'creationTimestamp' => $node->created_at_k8s?->toISOString(),
                'resourceVersion' => $node->resource_version,
                'labels' => $node->labels ?? [],
                'annotations' => $node->annotations ?? [],
            ],
            'spec' => [
                'taints' => $node->taints ?? [],
            ],
            'status' => [
                'conditions' => $node->conditions ?? [],
                'addresses' => $node->addresses ?? [],
                'nodeInfo' => [
                    'osImage' => $node->os_image,
                    'kernelVersion' => $node->kernel_version,
                    'containerRuntimeVersion' => $node->container_runtime,
                    'kubeletVersion' => $node->kubelet_version,
                    'architecture' => $node->architecture,
                    'operatingSystem' => $node->operating_system,
                ],
                'capacity' => $node->capacity ?? [],
                'allocatable' => $node->allocatable ?? [],
            ],
            // Additional computed fields
            '_db_id' => $node->id,
            '_status' => $node->status,
            '_roles' => $node->roles_string,
            '_pods_count' => $node->total_pods_count,
            '_running_pods_count' => $node->running_pods_count,
            '_cpu_usage_percentage' => $node->cpu_usage_percentage,
            '_memory_usage_percentage' => $node->memory_usage_percentage,
            '_last_synced' => $node->last_synced_at?->diffForHumans(),
        ];
    }

    /**
     * Convert database pod model to API-like format
     */
    protected function convertPodToApiFormat(KubernetesNodePod $pod)
    {
        return [
            'metadata' => [
                'name' => $pod->pod_name,
                'namespace' => $pod->namespace,
                'uid' => $pod->uid,
                'creationTimestamp' => $pod->created_at_k8s?->toISOString(),
                'labels' => $pod->labels ?? [],
                'annotations' => $pod->annotations ?? [],
            ],
            'spec' => [
                'nodeName' => $pod->node_name,
                'containers' => $pod->containers ?? [],
            ],
            'status' => [
                'phase' => $pod->status,
                'conditions' => [
                    [
                        'type' => 'Ready',
                        'status' => $pod->ready ? 'True' : 'False',
                    ]
                ],
                'containerStatuses' => $pod->container_statuses ?? [],
            ],
            // Additional computed fields
            '_ready_status' => $pod->ready_status,
            '_restart_count' => $pod->restart_count,
            '_age' => $pod->age,
        ];
    }

    /**
     * Convert database event model to API-like format
     */
    protected function convertEventToApiFormat(KubernetesNodeEvent $event)
    {
        return [
            'metadata' => [
                'uid' => $event->uid,
            ],
            'type' => $event->type,
            'reason' => $event->reason,
            'message' => $event->message,
            'source' => [
                'component' => $event->source_component,
                'host' => $event->source_host,
            ],
            'firstTimestamp' => $event->first_timestamp?->toISOString(),
            'lastTimestamp' => $event->last_timestamp?->toISOString(),
            'count' => $event->count,
            'involvedObject' => [
                'kind' => $event->involved_object_kind,
                'name' => $event->involved_object_name,
                'namespace' => $event->involved_object_namespace,
                'uid' => $event->involved_object_uid,
            ],
            // Additional computed fields
            '_age' => $event->age,
            '_is_warning' => $event->is_warning,
        ];
    }

    // Keep for backward compatibility
    public function loadNodes()
    {
        $this->loadData();
    }

    public function formatAge($timestamp)
    {
        if (!$timestamp) {
            return 'N/A';
        }

        $creationTime = Carbon::parse($timestamp);
        $now = Carbon::now();

        // Calculate total difference in various units
        $diffInSeconds = $creationTime->diffInSeconds($now);
        $diffInMinutes = $creationTime->diffInMinutes($now);
        $diffInHours = $creationTime->diffInHours($now);
        $diffInDays = $creationTime->diffInDays($now);

        // Calculate years and remaining days (Lens IDE format: 2y83d)
        $years = intval($diffInDays / 365);
        $remainingDays = $diffInDays % 365;

        if ($years > 0) {
            if ($remainingDays > 0) {
                return $years . 'y' . $remainingDays . 'd';
            } else {
                return $years . 'y';
            }
        }

        // For less than a year, show days
        if ($diffInDays >= 1) {
            return $diffInDays . 'd';
        }

        // For less than a day, show hours
        if ($diffInHours >= 1) {
            return $diffInHours . 'h';
        }

        // For less than an hour, show minutes
        if ($diffInMinutes >= 1) {
            return $diffInMinutes . 'm';
        }

        // For less than a minute, show seconds
        return $diffInSeconds . 's';
    }

    public function getNodeConditions($node)
    {
        $conditions = collect($node['status']['conditions'] ?? []);
        $readyCondition = $conditions->firstWhere('type', 'Ready');

        if ($readyCondition && $readyCondition['status'] === 'True') {
            return 'Ready';
        }

        // Check for problematic conditions
        $problemConditions = $conditions->filter(function ($condition) {
            return $condition['status'] === 'True' &&
                   in_array($condition['type'], ['MemoryPressure', 'DiskPressure', 'PIDPressure', 'NetworkUnavailable']);
        });

        if ($problemConditions->isNotEmpty()) {
            return $problemConditions->pluck('type')->join(', ');
        }

        return $readyCondition ? 'Not Ready' : 'Unknown';
    }

    public function getNodeWarnings($node)
    {
        $conditions = collect($node['status']['conditions'] ?? []);
        $warnings = [];

        // Check for warning conditions
        $warningConditions = $conditions->filter(function ($condition) {
            return $condition['status'] === 'True' &&
                   in_array($condition['type'], ['MemoryPressure', 'DiskPressure', 'PIDPressure']);
        });

        if ($warningConditions->isNotEmpty()) {
            $warnings = array_merge($warnings, $warningConditions->pluck('type')->toArray());
        }

        // Check for unschedulable nodes
        if (isset($node['spec']['unschedulable']) && $node['spec']['unschedulable']) {
            $warnings[] = 'Unschedulable';
        }

        return count($warnings) > 0 ? implode(', ', $warnings) : '-';
    }

    public function getNodeTaints($node)
    {
        $taints = $node['spec']['taints'] ?? [];
        return count($taints);
    }

    public function getNodeRoles($node)
    {
        $labels = $node['metadata']['labels'] ?? [];
        $roles = [];

        // Check for standard node-role.kubernetes.io/ labels
        foreach ($labels as $key => $value) {
            if (str_starts_with($key, 'node-role.kubernetes.io/')) {
                $role = str_replace('node-role.kubernetes.io/', '', $key);
                $roles[] = $role;
            }
        }

        // Check for legacy master labels (older Kubernetes versions)
        if (isset($labels['kubernetes.io/role']) && $labels['kubernetes.io/role'] === 'master') {
            $roles[] = 'master';
        }

        // Check for control-plane labels (newer Kubernetes versions)
        if (isset($labels['node-role.kubernetes.io/control-plane'])) {
            $roles[] = 'control-plane';
        }

        // Check for master labels (some distributions)
        if (isset($labels['node-role.kubernetes.io/master'])) {
            $roles[] = 'master';
        }

        // Remove duplicates and join
        $roles = array_unique($roles);

        return count($roles) > 0 ? implode(', ', $roles) : 'worker';
    }

    protected function getTableData()
    {
        $filteredNodes = collect($this->nodes);

        // Apply search filter
        if (!empty($this->searchTerm)) {
            $filteredNodes = $filteredNodes->filter(function ($node) {
                $name = $node['metadata']['name'] ?? '';
                return stripos($name, $this->searchTerm) !== false;
            });
        }

        // Apply sorting
        if (!empty($this->sortField)) {
            $filteredNodes = $filteredNodes->sortBy(function ($node) {
                switch ($this->sortField) {
                    case 'name':
                        return $node['metadata']['name'] ?? '';
                    case 'status':
                        return $this->getNodeStatus($node);
                    case 'roles':
                        return $this->getNodeRoles($node);
                    case 'age':
                        return $node['metadata']['creationTimestamp'] ?? '';
                    case 'version':
                        return $node['status']['nodeInfo']['kubeletVersion'] ?? '';
                    default:
                        return '';
                }
            }, SORT_REGULAR, $this->sortDirection === 'desc');
        }

        // Update total count for pagination
        $this->totalItems = $filteredNodes->count();

        // Apply pagination
        $paginatedNodes = $filteredNodes->forPage($this->currentPage, $this->perPage);

        return $paginatedNodes->values()->all();
    }

    protected function getTableColumns()
    {
        return [
            [
                'field' => 'name',
                'label' => 'Name',
                'sortable' => true
            ],
            [
                'field' => 'status',
                'label' => 'Status',
                'sortable' => true
            ],
            [
                'field' => 'warnings',
                'label' => '<svg class="w-4 h-4 mx-auto text-yellow-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>',
                'sortable' => false,
                'is_html' => true
            ],
            [
                'field' => 'roles',
                'label' => 'Roles',
                'sortable' => true
            ],
            [
                'field' => 'age',
                'label' => 'Age',
                'sortable' => true
            ],
            [
                'field' => 'version',
                'label' => 'Version',
                'sortable' => true
            ]
        ];
    }

    private function getNodeStatus($node)
    {
        $conditions = $node['status']['conditions'] ?? [];

        foreach ($conditions as $condition) {
            if ($condition['type'] === 'Ready') {
                return $condition['status'] === 'True' ? 'Ready' : 'Not Ready';
            }
        }

        return 'Unknown';
    }

    private function hasNodeWarnings($node)
    {
        $conditions = $node['status']['conditions'] ?? [];

        foreach ($conditions as $condition) {
            if ($condition['type'] !== 'Ready' && $condition['status'] === 'True') {
                return true;
            }
        }

        return false;
    }



    public function selectNode($nodeName)
    {
        $this->selectedNode = $nodeName;
        $this->showNodeDetails = true;

        // Load node data from database immediately
        $this->loadNodeDetailsFromDatabase();
    }

    public function closeNodeDetails()
    {
        $this->showNodeDetails = false;
        $this->selectedNode = null;
        $this->selectedNodeData = null;
        $this->selectedNodePods = [];
        $this->selectedNodeEvents = [];
        $this->selectedNodeMetrics = null;
    }

    public function loadNodeDetailsFromDatabase()
    {
        if (!$this->selectedNode) return;

        $this->nodeDetailsLoading = true;

        try {
            // Load node with relationships from database
            $node = KubernetesNode::forCluster($this->selectedCluster)
                ->where('name', $this->selectedNode)
                ->with(['pods' => function($query) {
                    $query->active()->orderBy('pod_name');
                }, 'recentEvents' => function($query) {
                    $query->orderBy('last_timestamp', 'desc')->limit(20);
                }])
                ->first();

            if (!$node) {
                throw new \Exception('Node not found in database');
            }

            // Set node data in API-compatible format
            $this->selectedNodeData = $this->convertNodeToApiFormat($node);

            // Convert pods to API format
            $this->selectedNodePods = $node->pods->map(function ($pod) {
                return $this->convertPodToApiFormat($pod);
            })->toArray();

            // Convert events to API format
            $this->selectedNodeEvents = $node->recentEvents->map(function ($event) {
                return $this->convertEventToApiFormat($event);
            })->toArray();

            // Set metrics if available
            $this->selectedNodeMetrics = null;
            if ($node->cpu_usage_cores || $node->memory_usage_bytes) {
                $this->selectedNodeMetrics = [
                    'metadata' => ['name' => $node->name],
                    'usage' => [
                        'cpu' => $node->cpu_usage_cores . 'n',
                        'memory' => $node->memory_usage_bytes . '',
                    ],
                    'timestamp' => $node->metrics_updated_at?->toISOString(),
                ];
            }

        } catch (\Exception $e) {
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => 'Failed to load node details: ' . $e->getMessage()
            ]);
        } finally {
            $this->nodeDetailsLoading = false;
        }
    }

    public function loadNodeDetailsAsync()
    {
        $this->loadNodeDetailsFromDatabase();
    }

    public function loadNodeDetails()
    {
        $this->loadNodeDetailsAsync();
    }

    public function formatBytes($value)
    {
        if (!$value || $value === '0') return '0 B';

        // Handle Kubernetes memory format (e.g., "7901700Ki", "8Gi")
        if (is_string($value)) {
            $value = trim($value);

            // Extract number and unit
            if (preg_match('/^(\d+(?:\.\d+)?)([KMGT]i?)$/', $value, $matches)) {
                $number = (float) $matches[1];
                $unit = $matches[2];

                // Convert to bytes based on unit
                switch ($unit) {
                    case 'Ki':
                        $bytes = $number * 1024;
                        break;
                    case 'Mi':
                        $bytes = $number * 1024 * 1024;
                        break;
                    case 'Gi':
                        $bytes = $number * 1024 * 1024 * 1024;
                        break;
                    case 'Ti':
                        $bytes = $number * 1024 * 1024 * 1024 * 1024;
                        break;
                    case 'K':
                        $bytes = $number * 1000;
                        break;
                    case 'M':
                        $bytes = $number * 1000 * 1000;
                        break;
                    case 'G':
                        $bytes = $number * 1000 * 1000 * 1000;
                        break;
                    case 'T':
                        $bytes = $number * 1000 * 1000 * 1000 * 1000;
                        break;
                    default:
                        $bytes = $number;
                }
            } else {
                $bytes = (float) $value;
            }
        } else {
            $bytes = (float) $value;
        }

        if ($bytes == 0) return '0 B';

        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= (1 << (10 * $pow));

        return round($bytes, 2) . ' ' . $units[$pow];
    }

    public function formatCpu($cpu)
    {
        if (!$cpu || $cpu === '0') return '0 cores';

        // Handle millicores (e.g., "100m" = 0.1 cores)
        if (str_ends_with($cpu, 'm')) {
            $millicores = (int) str_replace('m', '', $cpu);
            $cores = $millicores / 1000;
            return $cores . ' cores';
        }

        // Handle regular cores (e.g., "4" = 4 cores)
        if (is_numeric($cpu)) {
            return $cpu . ' cores';
        }

        // Handle other formats
        return $cpu . ' cores';
    }

    public function getSelectedNodeConditions()
    {
        if (!$this->selectedNodeData) return [];

        return $this->selectedNodeData['status']['conditions'] ?? [];
    }

    public function getSelectedNodeRoles()
    {
        if (!$this->selectedNodeData) return 'worker';

        $labels = $this->selectedNodeData['metadata']['labels'] ?? [];
        $roles = [];

        foreach ($labels as $key => $value) {
            if (str_starts_with($key, 'node-role.kubernetes.io/')) {
                $role = str_replace('node-role.kubernetes.io/', '', $key);
                $roles[] = $role;
            }
        }

        if (isset($labels['kubernetes.io/role']) && $labels['kubernetes.io/role'] === 'master') {
            $roles[] = 'master';
        }

        $roles = array_unique($roles);
        return count($roles) > 0 ? implode(', ', $roles) : 'worker';
    }

    public function getPodStatus($pod)
    {
        $phase = $pod['status']['phase'] ?? 'Unknown';

        // Check container statuses for more detailed status
        $containerStatuses = $pod['status']['containerStatuses'] ?? [];
        foreach ($containerStatuses as $status) {
            if (isset($status['state']['waiting'])) {
                return $status['state']['waiting']['reason'] ?? 'Waiting';
            }
            if (isset($status['state']['terminated'])) {
                return $status['state']['terminated']['reason'] ?? 'Terminated';
            }
        }

        return $phase;
    }

    public function getPodReadyStatus($pod)
    {
        $conditions = $pod['status']['conditions'] ?? [];
        foreach ($conditions as $condition) {
            if ($condition['type'] === 'Ready') {
                return $condition['status'] === 'True';
            }
        }
        return false;
    }

    public function render()
    {
        return view('livewire.kubernetes.node-list')->layout('layouts.kubernetes');
    }
}

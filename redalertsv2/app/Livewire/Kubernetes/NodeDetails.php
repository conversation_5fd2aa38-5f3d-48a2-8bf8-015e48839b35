<?php

namespace App\Livewire\Kubernetes;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use App\Services\CachedKubernetesService;
use Carbon\Carbon;

class NodeDetails extends Component
{
    public $nodeName;
    public $node = null;
    public $pods = [];
    public $events = [];
    public $metrics = null;
    public $loading = true;
    public $error = null;
    public $selectedCluster = null;

    public function mount($nodeName)
    {
        $this->nodeName = $nodeName;

        // Check if user is authenticated
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        // Try both session keys for compatibility
        $this->selectedCluster = session('selectedCluster') ?? session('selected_cluster');
        if ($this->selectedCluster) {
            $this->loadData();
        } else {
            $this->error = 'Please select a cluster first';
            $this->loading = false;
        }
    }

    public function loadData()
    {
        $this->loading = true;
        $this->error = null;

        try {
            $kubeconfigPath = env('KUBECONFIG_PATH', storage_path('app/kubeconfigs')) . '/' . $this->selectedCluster;

            if (!file_exists($kubeconfigPath)) {
                throw new \Exception('Kubeconfig file not found: ' . $kubeconfigPath);
            }

            $service = new CachedKubernetesService($kubeconfigPath);

            // Get node details
            $nodesResponse = $service->getNodes();
            if (isset($nodesResponse['items'])) {
                $this->node = collect($nodesResponse['items'])->firstWhere('metadata.name', $this->nodeName);
                if (!$this->node) {
                    throw new \Exception('Node not found: ' . $this->nodeName);
                }
            } else {
                throw new \Exception('Failed to fetch nodes');
            }

            // Get pods running on this node
            $podsResponse = $service->getPods();
            if (isset($podsResponse['items'])) {
                $this->pods = collect($podsResponse['items'])
                    ->filter(function ($pod) {
                        return ($pod['spec']['nodeName'] ?? '') === $this->nodeName;
                    })
                    ->values()
                    ->all();
            }

            // Get events related to this node
            $eventsResponse = $service->getEvents();
            if (isset($eventsResponse['items'])) {
                $this->events = collect($eventsResponse['items'])
                    ->filter(function ($event) {
                        return ($event['involvedObject']['name'] ?? '') === $this->nodeName &&
                               ($event['involvedObject']['kind'] ?? '') === 'Node';
                    })
                    ->sortByDesc('lastTimestamp')
                    ->take(20)
                    ->values()
                    ->all();
            }

            // Get node metrics (if metrics-server is available)
            try {
                $metricsResponse = $service->getNodeMetrics();
                if (isset($metricsResponse['items']) && count($metricsResponse['items']) > 0) {
                    $this->metrics = collect($metricsResponse['items'])
                        ->firstWhere('metadata.name', $this->nodeName);
                }
            } catch (\Exception $e) {
                // Metrics server not available, continue without metrics
                $this->metrics = null;
            }

        } catch (\Exception $e) {
            $this->error = 'Failed to load node details: ' . $e->getMessage();
        } finally {
            $this->loading = false;
        }
    }

    public function refreshData()
    {
        try {
            // Refresh the selected cluster from session
            $this->selectedCluster = session('selectedCluster') ?? session('selected_cluster');

            if (!$this->selectedCluster) {
                $this->error = 'Please select a cluster first';
                return;
            }

            $kubeconfigPath = env('KUBECONFIG_PATH', storage_path('app/kubeconfigs')) . '/' . $this->selectedCluster;
            $service = new CachedKubernetesService($kubeconfigPath);

            // Force refresh cache
            $service->clearCache();
            $this->loadData();

            $this->dispatch('notify', [
                'type' => 'success',
                'message' => 'Node details refreshed successfully'
            ]);
        } catch (\Exception $e) {
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => 'Failed to refresh data: ' . $e->getMessage()
            ]);
        }
    }

    public function formatAge($timestamp)
    {
        if (!$timestamp) {
            return 'N/A';
        }

        $creationTime = Carbon::parse($timestamp);
        $now = Carbon::now();

        $diffInSeconds = $creationTime->diffInSeconds($now);
        $diffInMinutes = $creationTime->diffInMinutes($now);
        $diffInHours = $creationTime->diffInHours($now);
        $diffInDays = $creationTime->diffInDays($now);

        $years = intval($diffInDays / 365);
        $remainingDays = $diffInDays % 365;

        if ($years > 0) {
            if ($remainingDays > 0) {
                return $years . 'y' . $remainingDays . 'd';
            } else {
                return $years . 'y';
            }
        }

        if ($diffInDays >= 1) {
            return $diffInDays . 'd';
        }

        if ($diffInHours >= 1) {
            return $diffInHours . 'h';
        }

        if ($diffInMinutes >= 1) {
            return $diffInMinutes . 'm';
        }

        return $diffInSeconds . 's';
    }

    public function formatBytes($value)
    {
        if (!$value || $value === '0') return '0 B';

        // Handle Kubernetes memory format (e.g., "7901700Ki", "8Gi")
        if (is_string($value)) {
            $value = trim($value);

            // Extract number and unit
            if (preg_match('/^(\d+(?:\.\d+)?)([KMGT]i?)$/', $value, $matches)) {
                $number = (float) $matches[1];
                $unit = $matches[2];

                // Convert to bytes based on unit
                switch ($unit) {
                    case 'Ki':
                        $bytes = $number * 1024;
                        break;
                    case 'Mi':
                        $bytes = $number * 1024 * 1024;
                        break;
                    case 'Gi':
                        $bytes = $number * 1024 * 1024 * 1024;
                        break;
                    case 'Ti':
                        $bytes = $number * 1024 * 1024 * 1024 * 1024;
                        break;
                    case 'K':
                        $bytes = $number * 1000;
                        break;
                    case 'M':
                        $bytes = $number * 1000 * 1000;
                        break;
                    case 'G':
                        $bytes = $number * 1000 * 1000 * 1000;
                        break;
                    case 'T':
                        $bytes = $number * 1000 * 1000 * 1000 * 1000;
                        break;
                    default:
                        $bytes = $number;
                }
            } else {
                // If no unit, assume it's already in bytes
                $bytes = (float) $value;
            }
        } else {
            $bytes = (float) $value;
        }

        if ($bytes == 0) return '0 B';

        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= (1 << (10 * $pow));

        return round($bytes, 2) . ' ' . $units[$pow];
    }

    public function formatCpu($cpu)
    {
        if (!$cpu || $cpu === '0') return '0 cores';

        // Handle millicores (e.g., "100m" = 0.1 cores)
        if (str_ends_with($cpu, 'm')) {
            $millicores = (int) str_replace('m', '', $cpu);
            $cores = $millicores / 1000;
            return $cores . ' cores';
        }

        // Handle regular cores (e.g., "4" = 4 cores)
        if (is_numeric($cpu)) {
            return $cpu . ' cores';
        }

        // Handle other formats
        return $cpu . ' cores';
    }

    public function getNodeConditions()
    {
        if (!$this->node) return [];
        
        return $this->node['status']['conditions'] ?? [];
    }

    public function getNodeRoles()
    {
        if (!$this->node) return 'worker';
        
        $labels = $this->node['metadata']['labels'] ?? [];
        $roles = [];

        foreach ($labels as $key => $value) {
            if (str_starts_with($key, 'node-role.kubernetes.io/')) {
                $role = str_replace('node-role.kubernetes.io/', '', $key);
                $roles[] = $role;
            }
        }

        if (isset($labels['kubernetes.io/role']) && $labels['kubernetes.io/role'] === 'master') {
            $roles[] = 'master';
        }

        $roles = array_unique($roles);
        return count($roles) > 0 ? implode(', ', $roles) : 'worker';
    }

    public function getPodStatus($pod)
    {
        $phase = $pod['status']['phase'] ?? 'Unknown';
        
        // Check container statuses for more detailed status
        $containerStatuses = $pod['status']['containerStatuses'] ?? [];
        foreach ($containerStatuses as $status) {
            if (isset($status['state']['waiting'])) {
                return $status['state']['waiting']['reason'] ?? 'Waiting';
            }
            if (isset($status['state']['terminated'])) {
                return $status['state']['terminated']['reason'] ?? 'Terminated';
            }
        }
        
        return $phase;
    }

    public function getPodReadyStatus($pod)
    {
        $conditions = $pod['status']['conditions'] ?? [];
        foreach ($conditions as $condition) {
            if ($condition['type'] === 'Ready') {
                return $condition['status'] === 'True';
            }
        }
        return false;
    }

    public function render()
    {
        return view('livewire.kubernetes.node-details')->layout('layouts.kubernetes');
    }
}

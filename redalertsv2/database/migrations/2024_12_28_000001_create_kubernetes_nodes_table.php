<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('kubernetes_nodes', function (Blueprint $table) {
            $table->id();
            $table->string('cluster_name')->index();
            $table->string('name')->index();
            $table->string('uid')->unique();
            
            // Basic metadata
            $table->timestamp('created_at_k8s')->nullable();
            $table->string('resource_version')->nullable();
            $table->json('labels')->nullable();
            $table->json('annotations')->nullable();
            
            // Node status
            $table->string('status')->default('Unknown'); // Ready, NotReady, Unknown
            $table->json('conditions')->nullable();
            $table->json('addresses')->nullable(); // Internal/External IPs
            
            // Node info
            $table->string('os_image')->nullable();
            $table->string('kernel_version')->nullable();
            $table->string('container_runtime')->nullable();
            $table->string('kubelet_version')->nullable();
            $table->string('architecture')->nullable();
            $table->string('operating_system')->nullable();
            
            // Capacity and allocatable resources
            $table->json('capacity')->nullable();
            $table->json('allocatable')->nullable();
            
            // Node roles (master, worker, etc.)
            $table->json('roles')->nullable();
            $table->json('taints')->nullable();
            
            // Metrics (if available)
            $table->bigInteger('cpu_usage_cores')->nullable(); // in nanocores
            $table->bigInteger('memory_usage_bytes')->nullable();
            $table->timestamp('metrics_updated_at')->nullable();
            
            // Sync tracking
            $table->timestamp('last_synced_at')->nullable();
            $table->boolean('is_active')->default(true);
            
            $table->timestamps();
            
            // Indexes for fast queries
            $table->unique(['cluster_name', 'name']); // Unique constraint for foreign key
            $table->index(['cluster_name', 'status']);
            $table->index(['cluster_name', 'is_active']);
            $table->index('last_synced_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('kubernetes_nodes');
    }
};

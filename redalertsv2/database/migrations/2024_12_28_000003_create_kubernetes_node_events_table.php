<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('kubernetes_node_events', function (Blueprint $table) {
            $table->id();
            $table->string('cluster_name')->index();
            $table->string('node_name')->index();
            $table->string('uid')->unique();
            
            // Event details
            $table->string('type')->default('Normal'); // Normal, Warning
            $table->string('reason')->nullable();
            $table->text('message')->nullable();
            $table->string('source_component')->nullable();
            $table->string('source_host')->nullable();
            
            // Event timing
            $table->timestamp('first_timestamp')->nullable();
            $table->timestamp('last_timestamp')->nullable();
            $table->integer('count')->default(1);
            
            // Involved object
            $table->string('involved_object_kind')->default('Node');
            $table->string('involved_object_name')->nullable();
            $table->string('involved_object_namespace')->nullable();
            $table->string('involved_object_uid')->nullable();
            
            // Sync tracking
            $table->timestamp('last_synced_at')->nullable();
            $table->boolean('is_active')->default(true);
            
            $table->timestamps();
            
            // Indexes for fast queries
            $table->index(['cluster_name', 'node_name']);
            $table->index(['cluster_name', 'type']);
            $table->index(['node_name', 'type']);
            $table->index('last_timestamp');
            $table->index('last_synced_at');
            
            // Foreign key constraint
            $table->foreign(['cluster_name', 'node_name'])
                  ->references(['cluster_name', 'name'])
                  ->on('kubernetes_nodes')
                  ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('kubernetes_node_events');
    }
};

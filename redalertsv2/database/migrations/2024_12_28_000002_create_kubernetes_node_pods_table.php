<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('kubernetes_node_pods', function (Blueprint $table) {
            $table->id();
            $table->string('cluster_name')->index();
            $table->string('node_name')->index();
            $table->string('pod_name')->index();
            $table->string('namespace')->index();
            $table->string('uid')->unique();
            
            // Pod status
            $table->string('status')->default('Unknown'); // Running, Pending, Failed, etc.
            $table->string('phase')->nullable();
            $table->boolean('ready')->default(false);
            $table->integer('restart_count')->default(0);
            
            // Pod metadata
            $table->timestamp('created_at_k8s')->nullable();
            $table->json('labels')->nullable();
            $table->json('annotations')->nullable();
            
            // Resource requests and limits
            $table->json('resource_requests')->nullable();
            $table->json('resource_limits')->nullable();
            
            // Container info
            $table->json('containers')->nullable();
            $table->json('container_statuses')->nullable();
            
            // Sync tracking
            $table->timestamp('last_synced_at')->nullable();
            $table->boolean('is_active')->default(true);
            
            $table->timestamps();
            
            // Indexes for fast queries
            $table->index(['cluster_name', 'node_name']);
            $table->index(['cluster_name', 'namespace']);
            $table->index(['cluster_name', 'status']);
            $table->index(['node_name', 'status']);
            $table->index('last_synced_at');
            
            // Foreign key constraint
            $table->foreign(['cluster_name', 'node_name'])
                  ->references(['cluster_name', 'name'])
                  ->on('kubernetes_nodes')
                  ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('kubernetes_node_pods');
    }
};

<div class="p-6">
    <!-- Breadcrumb -->
    <nav class="flex mb-4" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="{{ route('kubernetes.nodes') }}" wire:navigate class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-red-600">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                    </svg>
                    Nodes
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">{{ $nodeName }}</span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Node Details: {{ $nodeName }}</h1>
            @if($node)
                <p class="text-gray-600">{{ $this->getNodeRoles() }} • Created {{ $this->formatAge($node['metadata']['creationTimestamp'] ?? null) }} ago</p>
            @endif
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('kubernetes.nodes') }}" wire:navigate
               class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md transition duration-200">
                ← Back to Nodes
            </a>
            <button wire:click="refreshData"
                    class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md transition duration-200">
                🔄 Refresh
            </button>
        </div>
    </div>

    @if($loading)
        <div class="flex justify-center items-center h-64">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600"></div>
        </div>
    @elseif($error)
        <div class="bg-red-50 border border-red-200 rounded-md p-4">
            <div class="flex">
                <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                </svg>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">Error</h3>
                    <p class="text-sm text-red-700 mt-1">{{ $error }}</p>
                </div>
            </div>
        </div>
    @elseif($node)
        <!-- Node Details Content -->
        <div class="space-y-6">
            <!-- Metrics Section (if available) -->
            @if($metrics)
                <div class="bg-white rounded-lg shadow-md">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h2 class="text-lg font-semibold text-gray-900">Current Metrics</h2>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            <!-- CPU Usage -->
                            <div class="bg-blue-50 rounded-lg p-4">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-500">CPU Usage</div>
                                        <div class="text-2xl font-bold text-gray-900">{{ $this->formatCpu($metrics['usage']['cpu'] ?? '0') }}</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Memory Usage -->
                            <div class="bg-green-50 rounded-lg p-4">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-500">Memory Usage</div>
                                        <div class="text-2xl font-bold text-gray-900">{{ $this->formatBytes($metrics['usage']['memory'] ?? '0') }}</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Pod Count -->
                            <div class="bg-purple-50 rounded-lg p-4">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-500">Running Pods</div>
                                        <div class="text-2xl font-bold text-gray-900">{{ count($pods) }}</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Node Status -->
                            <div class="bg-red-50 rounded-lg p-4">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <svg class="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-500">Status</div>
                                        <div class="text-2xl font-bold text-gray-900">
                                            @php
                                                $conditions = $this->getNodeConditions();
                                                $readyCondition = collect($conditions)->firstWhere('type', 'Ready');
                                                $status = $readyCondition && $readyCondition['status'] === 'True' ? 'Ready' : 'Not Ready';
                                            @endphp
                                            {{ $status }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
            <!-- Properties Section -->
            <div class="bg-white rounded-lg shadow-md">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Properties</h2>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <!-- Basic Info -->
                        <div>
                            <h3 class="text-sm font-medium text-gray-500 mb-2">Basic Information</h3>
                            <div class="space-y-2 text-sm">
                                <div><span class="font-medium">Name:</span> {{ $node['metadata']['name'] ?? 'N/A' }}</div>
                                <div><span class="font-medium">Created:</span> {{ $this->formatAge($node['metadata']['creationTimestamp'] ?? null) }} ago</div>
                                <div><span class="font-medium">Roles:</span> {{ $this->getNodeRoles() }}</div>
                                <div><span class="font-medium">UID:</span> {{ $node['metadata']['uid'] ?? 'N/A' }}</div>
                            </div>
                        </div>

                        <!-- Node Info -->
                        <div>
                            <h3 class="text-sm font-medium text-gray-500 mb-2">System Information</h3>
                            <div class="space-y-2 text-sm">
                                <div><span class="font-medium">OS:</span> {{ $node['status']['nodeInfo']['operatingSystem'] ?? 'N/A' }}</div>
                                <div><span class="font-medium">OS Image:</span> {{ $node['status']['nodeInfo']['osImage'] ?? 'N/A' }}</div>
                                <div><span class="font-medium">Kernel:</span> {{ $node['status']['nodeInfo']['kernelVersion'] ?? 'N/A' }}</div>
                                <div><span class="font-medium">Architecture:</span> {{ $node['status']['nodeInfo']['architecture'] ?? 'N/A' }}</div>
                            </div>
                        </div>

                        <!-- Runtime Info -->
                        <div>
                            <h3 class="text-sm font-medium text-gray-500 mb-2">Runtime Information</h3>
                            <div class="space-y-2 text-sm">
                                <div><span class="font-medium">Container Runtime:</span> {{ $node['status']['nodeInfo']['containerRuntimeVersion'] ?? 'N/A' }}</div>
                                <div><span class="font-medium">Kubelet:</span> {{ $node['status']['nodeInfo']['kubeletVersion'] ?? 'N/A' }}</div>
                                <div><span class="font-medium">Kube-proxy:</span> {{ $node['status']['nodeInfo']['kubeProxyVersion'] ?? 'N/A' }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- Addresses -->
                    @if(isset($node['status']['addresses']) && count($node['status']['addresses']) > 0)
                        <div class="mt-6">
                            <h3 class="text-sm font-medium text-gray-500 mb-2">Addresses</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                @foreach($node['status']['addresses'] as $address)
                                    <div class="text-sm">
                                        <span class="font-medium">{{ $address['type'] }}:</span> {{ $address['address'] }}
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Capacity and Allocatable -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Capacity -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h2 class="text-lg font-semibold text-gray-900">Capacity</h2>
                    </div>
                    <div class="p-6">
                        <div class="space-y-3">
                            @if(isset($node['status']['capacity']))
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-gray-600">CPU:</span>
                                    <span class="text-sm text-gray-900">{{ $this->formatCpu($node['status']['capacity']['cpu'] ?? '0') }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-gray-600">Memory:</span>
                                    <span class="text-sm text-gray-900">{{ $this->formatBytes(($node['status']['capacity']['memory'] ?? '0Ki')) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-gray-600">Ephemeral Storage:</span>
                                    <span class="text-sm text-gray-900">{{ $this->formatBytes(($node['status']['capacity']['ephemeral-storage'] ?? '0Ki')) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-gray-600">Pods:</span>
                                    <span class="text-sm text-gray-900">{{ $node['status']['capacity']['pods'] ?? '0' }}</span>
                                </div>
                                @if(isset($node['status']['capacity']['hugepages-1Gi']))
                                    <div class="flex justify-between">
                                        <span class="text-sm font-medium text-gray-600">Hugepages-1Gi:</span>
                                        <span class="text-sm text-gray-900">{{ $node['status']['capacity']['hugepages-1Gi'] }}</span>
                                    </div>
                                @endif
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Allocatable -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h2 class="text-lg font-semibold text-gray-900">Allocatable</h2>
                    </div>
                    <div class="p-6">
                        <div class="space-y-3">
                            @if(isset($node['status']['allocatable']))
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-gray-600">CPU:</span>
                                    <span class="text-sm text-gray-900">{{ $this->formatCpu($node['status']['allocatable']['cpu'] ?? '0') }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-gray-600">Memory:</span>
                                    <span class="text-sm text-gray-900">{{ $this->formatBytes(($node['status']['allocatable']['memory'] ?? '0Ki')) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-gray-600">Ephemeral Storage:</span>
                                    <span class="text-sm text-gray-900">{{ $this->formatBytes(($node['status']['allocatable']['ephemeral-storage'] ?? '0Ki')) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-gray-600">Pods:</span>
                                    <span class="text-sm text-gray-900">{{ $node['status']['allocatable']['pods'] ?? '0' }}</span>
                                </div>
                                @if(isset($node['status']['allocatable']['hugepages-1Gi']))
                                    <div class="flex justify-between">
                                        <span class="text-sm font-medium text-gray-600">Hugepages-1Gi:</span>
                                        <span class="text-sm text-gray-900">{{ $node['status']['allocatable']['hugepages-1Gi'] }}</span>
                                    </div>
                                @endif
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Conditions -->
            <div class="bg-white rounded-lg shadow-md">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Conditions</h2>
                </div>
                <div class="p-6">
                    @if(count($this->getNodeConditions()) > 0)
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reason</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Message</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Transition</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($this->getNodeConditions() as $condition)
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ $condition['type'] ?? 'N/A' }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ ($condition['status'] ?? '') === 'True' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                                    {{ $condition['status'] ?? 'Unknown' }}
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $condition['reason'] ?? 'N/A' }}</td>
                                            <td class="px-6 py-4 text-sm text-gray-900">{{ $condition['message'] ?? 'N/A' }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $this->formatAge($condition['lastTransitionTime'] ?? null) }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-gray-500 text-sm">No conditions available</p>
                    @endif
                </div>
            </div>

            <!-- Pods Section -->
            <div class="bg-white rounded-lg shadow-md">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Pods ({{ count($pods) }})</h2>
                </div>
                <div class="p-6">
                    @if(count($pods) > 0)
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Namespace</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ready</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Restarts</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Age</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($pods as $pod)
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ $pod['metadata']['name'] ?? 'N/A' }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $pod['metadata']['namespace'] ?? 'N/A' }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $this->getPodReadyStatus($pod) ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                                    {{ $this->getPodReadyStatus($pod) ? 'Ready' : 'Not Ready' }}
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                                    @if($this->getPodStatus($pod) === 'Running') bg-green-100 text-green-800
                                                    @elseif(in_array($this->getPodStatus($pod), ['Pending', 'ContainerCreating'])) bg-yellow-100 text-yellow-800
                                                    @else bg-red-100 text-red-800
                                                    @endif">
                                                    {{ $this->getPodStatus($pod) }}
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                @php
                                                    $restarts = 0;
                                                    if (isset($pod['status']['containerStatuses'])) {
                                                        foreach ($pod['status']['containerStatuses'] as $status) {
                                                            $restarts += $status['restartCount'] ?? 0;
                                                        }
                                                    }
                                                @endphp
                                                {{ $restarts }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $this->formatAge($pod['metadata']['creationTimestamp'] ?? null) }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-gray-500 text-sm">No pods running on this node</p>
                    @endif
                </div>
            </div>

            <!-- Events Section -->
            <div class="bg-white rounded-lg shadow-md">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Events ({{ count($events) }})</h2>
                </div>
                <div class="p-6">
                    @if(count($events) > 0)
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reason</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Message</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Count</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Age</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($events as $event)
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ ($event['type'] ?? '') === 'Warning' ? 'bg-yellow-100 text-yellow-800' : 'bg-blue-100 text-blue-800' }}">
                                                    {{ $event['type'] ?? 'Normal' }}
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ $event['reason'] ?? 'N/A' }}</td>
                                            <td class="px-6 py-4 text-sm text-gray-900">{{ $event['message'] ?? 'N/A' }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $event['count'] ?? 1 }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $this->formatAge($event['lastTimestamp'] ?? $event['firstTimestamp'] ?? null) }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-gray-500 text-sm">No recent events for this node</p>
                    @endif
                </div>
            </div>

            <!-- Labels Section -->
            @if(isset($node['metadata']['labels']) && count($node['metadata']['labels']) > 0)
                <div class="bg-white rounded-lg shadow-md">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h2 class="text-lg font-semibold text-gray-900">Labels</h2>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            @foreach($node['metadata']['labels'] as $key => $value)
                                <div class="bg-gray-50 rounded-md p-3">
                                    <div class="text-xs font-medium text-gray-600 mb-1">{{ $key }}</div>
                                    <div class="text-sm text-gray-900">{{ $value }}</div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif

            <!-- Annotations Section -->
            @if(isset($node['metadata']['annotations']) && count($node['metadata']['annotations']) > 0)
                <div class="bg-white rounded-lg shadow-md">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h2 class="text-lg font-semibold text-gray-900">Annotations</h2>
                    </div>
                    <div class="p-6">
                        <div class="space-y-3">
                            @foreach($node['metadata']['annotations'] as $key => $value)
                                <div class="border-b border-gray-100 pb-3 last:border-b-0">
                                    <div class="text-xs font-medium text-gray-600 mb-1">{{ $key }}</div>
                                    <div class="text-sm text-gray-900 break-all">{{ $value }}</div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif
        </div>
    @endif
</div>

<div class="flex h-screen overflow-hidden" x-data="{ showPanel: @entangle('showNodeDetails') }">
    <!-- Main Content Area -->
    <div class="flex-1 flex flex-col overflow-hidden transition-all duration-300 ease-in-out"
         :class="{ 'mr-[600px]': showPanel }">
        <x-kubernetes-table
            title="Nodes"
            :all-data="$nodes"
            :columns="$this->getTableColumns()"
            :loading="$loading"
            :error="$error"
            :namespaces="[]"
            :show-namespace-filter="false"
            :show-refresh="true"
            refresh-method="refreshData"
        >
            <template x-for="node in paginatedData" :key="node.metadata.name">
                <tr class="hover:bg-gray-50 cursor-pointer"
                    :class="{ 'bg-red-50 border-l-4 border-red-500': $wire.selectedNode === node.metadata?.name }"
                    @click="$wire.selectNode(node.metadata?.name)">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600 hover:text-red-700"
                        x-text="node.metadata?.name || 'Unknown'">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                              :class="getNodeStatusClass(node)"
                              x-text="getNodeStatus(node)">
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-center">
                        <div x-show="hasNodeWarnings(node)" class="flex justify-center" :title="getNodeWarnings(node)">
                            <svg class="w-4 h-4 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" x-text="getNodeRoles(node)"></td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" x-text="formatAge(node.metadata?.creationTimestamp)"></td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" x-text="node.status?.nodeInfo?.kubeletVersion || 'Unknown'"></td>
                </tr>
            </template>
        </x-kubernetes-table>
    </div>

    <!-- Node Details Side Panel -->
    <div x-show="showPanel"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="transform translate-x-full"
         x-transition:enter-end="transform translate-x-0"
         x-transition:leave="transition ease-in duration-300"
         x-transition:leave-start="transform translate-x-0"
         x-transition:leave-end="transform translate-x-full"
         class="fixed right-0 top-0 h-full w-[600px] bg-gray-900 text-white shadow-2xl border-l border-gray-700 z-50 overflow-y-auto"
         style="display: none;">
            <!-- Panel Header -->
            <div class="sticky top-0 bg-gray-800 border-b border-gray-700 px-6 py-4 flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"></path>
                    </svg>
                    <div>
                        <h2 class="text-lg font-semibold text-white">{{ $selectedNode }}</h2>
                        @if($selectedNodeData)
                            <p class="text-sm text-gray-400">{{ $this->getSelectedNodeRoles() }}</p>
                        @endif
                    </div>
                </div>
                <button wire:click="closeNodeDetails" class="text-gray-400 hover:text-white">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            @if($selectedNodeData)
                <div class="p-6 space-y-8">
                    <!-- Properties Section -->
                    <div>
                        <h3 class="text-lg font-semibold text-white mb-4 border-b border-gray-700 pb-2">Properties</h3>
                        <div class="space-y-4">
                            <!-- Created -->
                            <div>
                                <div class="text-sm text-gray-400 mb-1">Created</div>
                                <div class="text-sm text-white">{{ $this->formatAge($selectedNodeData['metadata']['creationTimestamp'] ?? null) }} ago ({{ $selectedNodeData['metadata']['creationTimestamp'] ?? 'N/A' }})</div>
                            </div>

                            <!-- Name -->
                            <div>
                                <div class="text-sm text-gray-400 mb-1">Name</div>
                                <div class="text-sm text-white">{{ $selectedNodeData['metadata']['name'] ?? 'N/A' }}</div>
                            </div>

                            <!-- Labels -->
                            @if(isset($selectedNodeData['metadata']['labels']) && count($selectedNodeData['metadata']['labels']) > 0)
                                <div>
                                    <div class="text-sm text-gray-400 mb-2 flex items-center">
                                        Labels
                                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </div>
                                    <div class="space-y-1 max-h-32 overflow-y-auto">
                                        @foreach($selectedNodeData['metadata']['labels'] as $key => $value)
                                            <div class="text-xs bg-gray-800 rounded px-2 py-1 flex justify-between">
                                                <span class="text-blue-300">{{ $key }}</span>
                                                <span class="text-gray-300">{{ $value }}</span>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endif

                            <!-- Annotations -->
                            @if(isset($selectedNodeData['metadata']['annotations']) && count($selectedNodeData['metadata']['annotations']) > 0)
                                <div>
                                    <div class="text-sm text-gray-400 mb-2 flex items-center">
                                        Annotations
                                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </div>
                                    <div class="space-y-1 max-h-32 overflow-y-auto">
                                        @foreach(array_slice($selectedNodeData['metadata']['annotations'], 0, 5) as $key => $value)
                                            <div class="text-xs bg-gray-800 rounded px-2 py-1">
                                                <div class="text-blue-300 mb-1">{{ $key }}</div>
                                                <div class="text-gray-300 break-all">{{ Str::limit($value, 100) }}</div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Addresses -->
                    @if(isset($selectedNodeData['status']['addresses']) && count($selectedNodeData['status']['addresses']) > 0)
                        <div>
                            <div class="text-sm text-gray-400 mb-2">Addresses</div>
                            <div class="space-y-2">
                                @foreach($selectedNodeData['status']['addresses'] as $address)
                                    <div class="text-sm">
                                        <span class="text-gray-400">{{ $address['type'] }}:</span>
                                        <span class="text-white ml-2">{{ $address['address'] }}</span>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- OS Information -->
                    <div>
                        <div class="text-sm text-gray-400 mb-2">OS</div>
                        <div class="text-sm text-white">{{ $selectedNodeData['status']['nodeInfo']['operatingSystem'] ?? 'N/A' }} ({{ $selectedNodeData['status']['nodeInfo']['architecture'] ?? 'N/A' }})</div>
                    </div>

                    <!-- OS Image -->
                    <div>
                        <div class="text-sm text-gray-400 mb-2">OS Image</div>
                        <div class="text-sm text-white">{{ $selectedNodeData['status']['nodeInfo']['osImage'] ?? 'N/A' }}</div>
                    </div>

                    <!-- Kernel Version -->
                    <div>
                        <div class="text-sm text-gray-400 mb-2">Kernel version</div>
                        <div class="text-sm text-white">{{ $selectedNodeData['status']['nodeInfo']['kernelVersion'] ?? 'N/A' }}</div>
                    </div>

                    <!-- Container Runtime -->
                    <div>
                        <div class="text-sm text-gray-400 mb-2">Container runtime</div>
                        <div class="text-sm text-white">{{ $selectedNodeData['status']['nodeInfo']['containerRuntimeVersion'] ?? 'N/A' }}</div>
                    </div>

                    <!-- Kubelet Version -->
                    <div>
                        <div class="text-sm text-gray-400 mb-2">Kubelet version</div>
                        <div class="text-sm text-white">{{ $selectedNodeData['status']['nodeInfo']['kubeletVersion'] ?? 'N/A' }}</div>
                    </div>

                    <!-- Taints -->
                    @if(isset($selectedNodeData['spec']['taints']) && count($selectedNodeData['spec']['taints']) > 0)
                        <div>
                            <div class="text-sm text-gray-400 mb-2">Taints</div>
                            <div class="text-sm text-white">{{ $selectedNodeData['metadata']['labels']['node-role.kubernetes.io/master'] ?? 'NoSchedule' }}</div>
                        </div>
                    @else
                        <div>
                            <div class="text-sm text-gray-400 mb-2">Taints</div>
                            <div class="text-sm text-white">node-role.kubernetes.io/master:NoSchedule</div>
                        </div>
                    @endif

                    <!-- Conditions -->
                    <div>
                        <h3 class="text-lg font-semibold text-white mb-4 border-b border-gray-700 pb-2">Conditions</h3>
                        @if(count($this->getSelectedNodeConditions()) > 0)
                            <div class="space-y-3">
                                @foreach($this->getSelectedNodeConditions() as $condition)
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm text-gray-400">{{ $condition['type'] ?? 'N/A' }}</span>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded {{ ($condition['status'] ?? '') === 'True' ? 'bg-green-600 text-white' : 'bg-red-600 text-white' }}">
                                            {{ ($condition['status'] ?? '') === 'True' ? 'Ready' : 'Not Ready' }}
                                        </span>
                                    </div>
                                @endforeach
                            </div>
                        @endif
                    </div>

                    <!-- Capacity -->
                    <div>
                        <h3 class="text-lg font-semibold text-white mb-4 border-b border-gray-700 pb-2">Capacity</h3>
                        @if(isset($selectedNodeData['status']['capacity']))
                            <div class="overflow-x-auto">
                                <table class="w-full text-sm">
                                    <thead>
                                        <tr class="text-gray-400 border-b border-gray-700">
                                            <th class="text-left py-2">CPU</th>
                                            <th class="text-left py-2">Memory</th>
                                            <th class="text-left py-2">Ephemeral Storage</th>
                                            <th class="text-left py-2">Hugepages-1Gi</th>
                                            <th class="text-left py-2">Hugepages-2Mi</th>
                                            <th class="text-left py-2">Pods</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr class="text-white">
                                            <td class="py-2">{{ $selectedNodeData['status']['capacity']['cpu'] ?? '0' }}</td>
                                            <td class="py-2">{{ $this->formatBytes($selectedNodeData['status']['capacity']['memory'] ?? '0') }}</td>
                                            <td class="py-2">{{ $this->formatBytes($selectedNodeData['status']['capacity']['ephemeral-storage'] ?? '0') }}</td>
                                            <td class="py-2">{{ $selectedNodeData['status']['capacity']['hugepages-1Gi'] ?? '0' }}</td>
                                            <td class="py-2">{{ $selectedNodeData['status']['capacity']['hugepages-2Mi'] ?? '0' }}</td>
                                            <td class="py-2">{{ $selectedNodeData['status']['capacity']['pods'] ?? '0' }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        @endif
                    </div>

                    <!-- Allocatable -->
                    <div>
                        <h3 class="text-lg font-semibold text-white mb-4 border-b border-gray-700 pb-2">Allocatable</h3>
                        @if(isset($selectedNodeData['status']['allocatable']))
                            <div class="overflow-x-auto">
                                <table class="w-full text-sm">
                                    <thead>
                                        <tr class="text-gray-400 border-b border-gray-700">
                                            <th class="text-left py-2">CPU</th>
                                            <th class="text-left py-2">Memory</th>
                                            <th class="text-left py-2">Ephemeral Storage</th>
                                            <th class="text-left py-2">Hugepages-1Gi</th>
                                            <th class="text-left py-2">Hugepages-2Mi</th>
                                            <th class="text-left py-2">Pods</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr class="text-white">
                                            <td class="py-2">{{ $selectedNodeData['status']['allocatable']['cpu'] ?? '0' }}</td>
                                            <td class="py-2">{{ $this->formatBytes($selectedNodeData['status']['allocatable']['memory'] ?? '0') }}</td>
                                            <td class="py-2">{{ $this->formatBytes($selectedNodeData['status']['allocatable']['ephemeral-storage'] ?? '0') }}</td>
                                            <td class="py-2">{{ $selectedNodeData['status']['allocatable']['hugepages-1Gi'] ?? '0' }}</td>
                                            <td class="py-2">{{ $selectedNodeData['status']['allocatable']['hugepages-2Mi'] ?? '0' }}</td>
                                            <td class="py-2">{{ $selectedNodeData['status']['allocatable']['pods'] ?? '0' }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        @endif
                    </div>

                    <!-- Pods -->
                    <div>
                        <h3 class="text-lg font-semibold text-white mb-4 border-b border-gray-700 pb-2 flex items-center">
                            Pods
                            @if($nodeDetailsLoading)
                                <div class="ml-2 animate-spin rounded-full h-4 w-4 border-b-2 border-blue-400"></div>
                            @endif
                        </h3>
                        @if($nodeDetailsLoading)
                            <div class="text-sm text-gray-400">Loading pods...</div>
                        @elseif(count($selectedNodePods) > 0)
                            <div class="overflow-x-auto max-h-80 overflow-y-auto">
                                <table class="w-full text-sm">
                                    <thead class="sticky top-0 bg-gray-800">
                                        <tr class="text-gray-400 border-b border-gray-700">
                                            <th class="text-left py-2 px-2">
                                                <div class="flex items-center">
                                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11.5V14m0-2.5v-6a1.5 1.5 0 113 0m-3 6a1.5 1.5 0 00-3 0v2a1.5 1.5 0 003 0m0-2.5v-6a1.5 1.5 0 113 0m-3 6a1.5 1.5 0 00-3 0v2a1.5 1.5 0 003 0m6-2v2a1.5 1.5 0 01-3 0v-2a1.5 1.5 0 013 0zM9 5.5v3M9 11.5V14"></path>
                                                    </svg>
                                                    Name
                                                </div>
                                            </th>
                                            <th class="text-left py-2 px-2">Node</th>
                                            <th class="text-left py-2 px-2">Namespace</th>
                                            <th class="text-left py-2 px-2">Ready</th>
                                            <th class="text-left py-2 px-2">CPU</th>
                                            <th class="text-left py-2 px-2">Memory</th>
                                            <th class="text-left py-2 px-2">Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($selectedNodePods as $pod)
                                            <tr class="text-white border-b border-gray-800 hover:bg-gray-800">
                                                <td class="py-2 px-2">{{ $pod['metadata']['name'] ?? 'N/A' }}</td>
                                                <td class="py-2 px-2">{{ $selectedNode }}</td>
                                                <td class="py-2 px-2">{{ $pod['metadata']['namespace'] ?? 'N/A' }}</td>
                                                <td class="py-2 px-2">
                                                    <span class="text-xs">
                                                        {{ $this->getPodReadyStatus($pod) ? '1 / 1' : '0 / 1' }}
                                                    </span>
                                                </td>
                                                <td class="py-2 px-2">-</td>
                                                <td class="py-2 px-2">-</td>
                                                <td class="py-2 px-2">
                                                    <span class="inline-flex px-2 py-1 text-xs rounded {{ $this->getPodStatus($pod) === 'Running' ? 'bg-green-600 text-white' : ($this->getPodStatus($pod) === 'Pending' ? 'bg-yellow-600 text-white' : 'bg-red-600 text-white') }}">
                                                        {{ $this->getPodStatus($pod) }}
                                                    </span>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <p class="text-gray-400 text-sm">No pods running on this node</p>
                        @endif
                    </div>

                    <!-- Events -->
                    <div>
                        <h3 class="text-lg font-semibold text-white mb-4 border-b border-gray-700 pb-2 flex items-center">
                            Events
                            @if($nodeDetailsLoading)
                                <div class="ml-2 animate-spin rounded-full h-4 w-4 border-b-2 border-blue-400"></div>
                            @endif
                        </h3>
                        @if($nodeDetailsLoading)
                            <div class="text-sm text-gray-400">Loading events...</div>
                        @elseif(count($selectedNodeEvents) > 0)
                            <div class="text-sm text-gray-400">
                                Events found: {{ count($selectedNodeEvents) }}
                            </div>
                        @else
                            <div class="text-sm text-gray-400">
                                No events found
                            </div>
                        @endif
                    </div>
                </div>
            @else
                <div class="p-6">
                    <div class="text-center text-gray-400">
                        <svg class="mx-auto h-12 w-12 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <p class="mt-2 text-sm">Failed to load node details</p>
                        <button wire:click="loadNodeDetails" class="mt-2 text-blue-400 hover:text-blue-300 text-sm">
                            Try again
                        </button>
                    </div>
                </div>
            @endif
        </div>
</div>

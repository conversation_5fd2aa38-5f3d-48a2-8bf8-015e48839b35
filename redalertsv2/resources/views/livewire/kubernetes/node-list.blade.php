<div class="flex h-screen overflow-hidden" x-data="{ showPanel: @entangle('showNodeDetails') }">
    <!-- Main Content Area -->
    <div class="flex-1 flex flex-col overflow-hidden transition-all duration-300 ease-in-out"
         :class="{ 'mr-96': showPanel }">
        <x-kubernetes-table
            title="Nodes"
            :all-data="$nodes"
            :columns="$this->getTableColumns()"
            :loading="$loading"
            :error="$error"
            :namespaces="[]"
            :show-namespace-filter="false"
            :show-refresh="true"
            refresh-method="refreshData"
        >
            <template x-for="node in paginatedData" :key="node.metadata.name">
                <tr class="hover:bg-gray-50 cursor-pointer"
                    :class="{ 'bg-red-50 border-l-4 border-red-500': $wire.selectedNode === node.metadata?.name }"
                    @click="$wire.selectNode(node.metadata?.name)">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600 hover:text-red-700"
                        x-text="node.metadata?.name || 'Unknown'">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                              :class="getNodeStatusClass(node)"
                              x-text="getNodeStatus(node)">
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-center">
                        <div x-show="hasNodeWarnings(node)" class="flex justify-center" :title="getNodeWarnings(node)">
                            <svg class="w-4 h-4 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" x-text="getNodeRoles(node)"></td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" x-text="formatAge(node.metadata?.creationTimestamp)"></td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" x-text="node.status?.nodeInfo?.kubeletVersion || 'Unknown'"></td>
                </tr>
            </template>
        </x-kubernetes-table>
    </div>

    <!-- Node Details Side Panel -->
    <div x-show="showPanel"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="transform translate-x-full"
         x-transition:enter-end="transform translate-x-0"
         x-transition:leave="transition ease-in duration-300"
         x-transition:leave-start="transform translate-x-0"
         x-transition:leave-end="transform translate-x-full"
         class="fixed right-0 top-0 h-full w-96 bg-white shadow-2xl border-l border-gray-200 z-50 overflow-y-auto"
         style="display: none;">
            <!-- Panel Header -->
            <div class="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between">
                <div>
                    <h2 class="text-lg font-semibold text-gray-900">{{ $selectedNode }}</h2>
                    @if($selectedNodeData)
                        <p class="text-sm text-gray-600">{{ $this->getSelectedNodeRoles() }}</p>
                    @endif
                </div>
                <button wire:click="closeNodeDetails" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            @if($nodeDetailsLoading)
                <div class="flex justify-center items-center h-64">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600"></div>
                </div>
            @elseif($selectedNodeData)
                <div class="p-6 space-y-6">
                    <!-- Metrics Section (if available) -->
                    @if($selectedNodeMetrics)
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h3 class="text-sm font-semibold text-gray-900 mb-3">Current Metrics</h3>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">CPU:</span>
                                    <span class="text-sm font-medium">{{ $this->formatCpu($selectedNodeMetrics['usage']['cpu'] ?? '0') }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Memory:</span>
                                    <span class="text-sm font-medium">{{ $this->formatBytes($selectedNodeMetrics['usage']['memory'] ?? '0') }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Pods:</span>
                                    <span class="text-sm font-medium">{{ count($selectedNodePods) }}</span>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Properties -->
                    <div>
                        <h3 class="text-sm font-semibold text-gray-900 mb-3">Properties</h3>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Name:</span>
                                <span class="font-medium">{{ $selectedNodeData['metadata']['name'] ?? 'N/A' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Created:</span>
                                <span class="font-medium">{{ $this->formatAge($selectedNodeData['metadata']['creationTimestamp'] ?? null) }} ago</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">OS:</span>
                                <span class="font-medium">{{ $selectedNodeData['status']['nodeInfo']['operatingSystem'] ?? 'N/A' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Kernel:</span>
                                <span class="font-medium">{{ $selectedNodeData['status']['nodeInfo']['kernelVersion'] ?? 'N/A' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Container Runtime:</span>
                                <span class="font-medium">{{ $selectedNodeData['status']['nodeInfo']['containerRuntimeVersion'] ?? 'N/A' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Kubelet:</span>
                                <span class="font-medium">{{ $selectedNodeData['status']['nodeInfo']['kubeletVersion'] ?? 'N/A' }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Addresses -->
                    @if(isset($selectedNodeData['status']['addresses']) && count($selectedNodeData['status']['addresses']) > 0)
                        <div>
                            <h3 class="text-sm font-semibold text-gray-900 mb-3">Addresses</h3>
                            <div class="space-y-2">
                                @foreach($selectedNodeData['status']['addresses'] as $address)
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-600">{{ $address['type'] }}:</span>
                                        <span class="font-medium">{{ $address['address'] }}</span>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Capacity -->
                    <div>
                        <h3 class="text-sm font-semibold text-gray-900 mb-3">Capacity</h3>
                        @if(isset($selectedNodeData['status']['capacity']))
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">CPU:</span>
                                    <span class="font-medium">{{ $this->formatCpu($selectedNodeData['status']['capacity']['cpu'] ?? '0') }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Memory:</span>
                                    <span class="font-medium">{{ $this->formatBytes($selectedNodeData['status']['capacity']['memory'] ?? '0') }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Pods:</span>
                                    <span class="font-medium">{{ $selectedNodeData['status']['capacity']['pods'] ?? '0' }}</span>
                                </div>
                            </div>
                        @endif
                    </div>

                    <!-- Allocatable -->
                    <div>
                        <h3 class="text-sm font-semibold text-gray-900 mb-3">Allocatable</h3>
                        @if(isset($selectedNodeData['status']['allocatable']))
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">CPU:</span>
                                    <span class="font-medium">{{ $this->formatCpu($selectedNodeData['status']['allocatable']['cpu'] ?? '0') }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Memory:</span>
                                    <span class="font-medium">{{ $this->formatBytes($selectedNodeData['status']['allocatable']['memory'] ?? '0') }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Pods:</span>
                                    <span class="font-medium">{{ $selectedNodeData['status']['allocatable']['pods'] ?? '0' }}</span>
                                </div>
                            </div>
                        @endif
                    </div>

                    <!-- Conditions -->
                    <div>
                        <h3 class="text-sm font-semibold text-gray-900 mb-3">Conditions</h3>
                        @if(count($this->getSelectedNodeConditions()) > 0)
                            <div class="space-y-2">
                                @foreach($this->getSelectedNodeConditions() as $condition)
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-gray-600">{{ $condition['type'] ?? 'N/A' }}:</span>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ ($condition['status'] ?? '') === 'True' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                            {{ $condition['status'] ?? 'Unknown' }}
                                        </span>
                                    </div>
                                @endforeach
                            </div>
                        @endif
                    </div>

                    <!-- Pods -->
                    <div>
                        <h3 class="text-sm font-semibold text-gray-900 mb-3">Pods ({{ count($selectedNodePods) }})</h3>
                        @if(count($selectedNodePods) > 0)
                            <div class="space-y-2 max-h-64 overflow-y-auto">
                                @foreach($selectedNodePods as $pod)
                                    <div class="bg-gray-50 rounded p-3">
                                        <div class="flex justify-between items-start mb-2">
                                            <div class="font-medium text-sm">{{ $pod['metadata']['name'] ?? 'N/A' }}</div>
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $this->getPodReadyStatus($pod) ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                                {{ $this->getPodReadyStatus($pod) ? 'Ready' : 'Not Ready' }}
                                            </span>
                                        </div>
                                        <div class="text-xs text-gray-600 space-y-1">
                                            <div>Namespace: {{ $pod['metadata']['namespace'] ?? 'N/A' }}</div>
                                            <div>Status:
                                                <span class="inline-flex px-1 py-0.5 text-xs rounded {{ $this->getPodStatus($pod) === 'Running' ? 'bg-green-100 text-green-800' : ($this->getPodStatus($pod) === 'Pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                                                    {{ $this->getPodStatus($pod) }}
                                                </span>
                                            </div>
                                            <div>Age: {{ $this->formatAge($pod['metadata']['creationTimestamp'] ?? null) }}</div>
                                            @php
                                                $restarts = 0;
                                                if (isset($pod['status']['containerStatuses'])) {
                                                    foreach ($pod['status']['containerStatuses'] as $status) {
                                                        $restarts += $status['restartCount'] ?? 0;
                                                    }
                                                }
                                            @endphp
                                            @if($restarts > 0)
                                                <div>Restarts: {{ $restarts }}</div>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <p class="text-gray-500 text-sm">No pods running on this node</p>
                        @endif
                    </div>

                    <!-- Events -->
                    <div>
                        <h3 class="text-sm font-semibold text-gray-900 mb-3">Recent Events ({{ count($selectedNodeEvents) }})</h3>
                        @if(count($selectedNodeEvents) > 0)
                            <div class="space-y-2 max-h-64 overflow-y-auto">
                                @foreach($selectedNodeEvents as $event)
                                    <div class="bg-gray-50 rounded p-3">
                                        <div class="flex justify-between items-start mb-2">
                                            <div class="font-medium text-sm">{{ $event['reason'] ?? 'N/A' }}</div>
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ ($event['type'] ?? '') === 'Warning' ? 'bg-yellow-100 text-yellow-800' : 'bg-blue-100 text-blue-800' }}">
                                                {{ $event['type'] ?? 'Normal' }}
                                            </span>
                                        </div>
                                        <div class="text-xs text-gray-600 space-y-1">
                                            <div>{{ $event['message'] ?? 'N/A' }}</div>
                                            <div class="flex justify-between">
                                                <span>Count: {{ $event['count'] ?? 1 }}</span>
                                                <span>{{ $this->formatAge($event['lastTimestamp'] ?? $event['firstTimestamp'] ?? null) }} ago</span>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <p class="text-gray-500 text-sm">No recent events for this node</p>
                        @endif
                    </div>

                    <!-- Labels (if any) -->
                    @if(isset($selectedNodeData['metadata']['labels']) && count($selectedNodeData['metadata']['labels']) > 0)
                        <div>
                            <h3 class="text-sm font-semibold text-gray-900 mb-3">Labels</h3>
                            <div class="space-y-1 max-h-32 overflow-y-auto">
                                @foreach($selectedNodeData['metadata']['labels'] as $key => $value)
                                    <div class="text-xs bg-gray-50 rounded p-2">
                                        <div class="font-medium text-gray-700">{{ $key }}</div>
                                        <div class="text-gray-600">{{ $value }}</div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
            @else
                <div class="p-6">
                    <div class="text-center text-gray-500">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <p class="mt-2 text-sm">Failed to load node details</p>
                        <button wire:click="loadNodeDetails" class="mt-2 text-red-600 hover:text-red-700 text-sm">
                            Try again
                        </button>
                    </div>
                </div>
            @endif
        </div>
</div>

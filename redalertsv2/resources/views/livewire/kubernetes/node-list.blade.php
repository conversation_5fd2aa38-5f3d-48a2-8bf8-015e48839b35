<x-kubernetes-table
    title="Nodes"
    :all-data="$nodes"
    :columns="$this->getTableColumns()"
    :loading="$loading"
    :error="$error"
    :namespaces="[]"
    :show-namespace-filter="false"
    :show-refresh="true"
    refresh-method="refreshData"
>

    <template x-for="node in paginatedData" :key="node.metadata.name">
        <tr class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <a :href="`/kubernetes/nodes/${node.metadata?.name || 'unknown'}`"
                   class="text-red-600 hover:text-red-700 hover:underline"
                   x-text="node.metadata?.name || 'Unknown'">
                </a>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                      :class="getNodeStatusClass(node)"
                      x-text="getNodeStatus(node)">
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-center">
                <div x-show="hasNodeWarnings(node)" class="flex justify-center" :title="getNodeWarnings(node)">
                    <svg class="w-4 h-4 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" x-text="getNodeRoles(node)"></td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" x-text="formatAge(node.metadata?.creationTimestamp)"></td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" x-text="node.status?.nodeInfo?.kubeletVersion || 'Unknown'"></td>
        </tr>
    </template>

</x-kubernetes-table>
